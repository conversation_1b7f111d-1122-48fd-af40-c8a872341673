export const SEARCH_FIELD_NAMES = {
  searchText: "searchText",
  searchIn: "searchIn",
  inOut: "inOut",
  from: "from",
  to: "to",
  datePeriod: "datePeriod",
  flagged: "flagged",
  hasAttachments: "hasAttachments",
  readUnread: "readUnread",
};

export const SEARCH_FIELD_API_NAMES = {
  MSS_TextValue : SEARCH_FIELD_NAMES.searchText,
  MSS_TextIn : SEARCH_FIELD_NAMES.searchIn,
  MSS_InOut : SEARCH_FIELD_NAMES.inOut,
  MSS_From : SEARCH_FIELD_NAMES.from,
  MSS_To : SEARCH_FIELD_NAMES.to,
  MSS_TimePeriod : SEARCH_FIELD_NAMES.datePeriod,
  MSS_Flagged : SEARCH_FIELD_NAMES.flagged,
  MSS_Attachments : SEARCH_FIELD_NAMES.hasAttachments,
  MSS_Read : SEARCH_FIELD_NAMES.readUnread,
};

export const SEARCH_IN_OPTIONS = {
  subject: {
    name: "Subject",
    value: "1",
  },
  body: {
    name: "Body",
    value: "2",
  },
  attachments: {
    name: "Attachments",
    value: "4",
  },
};

export const DATE_PERIOD_OPTIONS = {
  today: {
    name: "Today",
    value: "5",
  },
  oneDay: {
    name: "1 day",
    value: "10",
  },
  threeDays: {
    name: "3 days",
    value: "15",
  },
  oneWeek: {
    name: "1 week",
    value: "20",
  },
  twoWeeks: {
    name: "2 weeks",
    value: "25",
  },
  oneMonth: {
    name: "1 month",
    value: "30",
  },
  threeMonths: {
    name: "3 months",
    value: "35",
  },
  sixMonths: {
    name: "6 months",
    value: "40",
  },
  oneYear: {
    name: "1 year",
    value: "45",
  },
  notSet: {
    name: "Not Set",
    value: null,
  },
};

export const HAS_ATTACHMENTS_OPTIONS = {
  yes: {
    name: "Yes",
    value: "1",
  },
  no: {
    name: "No",
    value: "2",
  },
  notSet: {
    name: "Not Set",
    value: null,
  },
};

export const READ_UNREAD_OPTIONS = {
  read: {
    name: "Read",
    value: "1",
  },
  unread: {
    name: "Unread",
    value: "2",
  },
  notSet: {
    name: "Not Set",
    value: null,
  },
};

export const FLAGGED_OPTIONS = {
  yes: {
    name: "Yes",
    value: "40",
  },
  no: {
    name: "No",
    value: "80",
  },
  notSet: {
    name: "Not Set",
    value: null,
  },
};

export const IN_OUT_OPTIONS = {
  incoming: {
    name: "Incoming",
    value: "1",
  },
  outgoing: {
    name: "Outgoing",
    value: "2",
  },
  notSet: {
    name: "Not Set",
    value: null,
  },
};

export const SEARCH_FIELD_NAMES_OPTIONS = {
  [SEARCH_FIELD_NAMES.searchIn]: Object.values(SEARCH_IN_OPTIONS),
  [SEARCH_FIELD_NAMES.datePeriod]: Object.values(DATE_PERIOD_OPTIONS),
  [SEARCH_FIELD_NAMES.hasAttachments]: Object.values(HAS_ATTACHMENTS_OPTIONS),
  [SEARCH_FIELD_NAMES.readUnread]: Object.values(READ_UNREAD_OPTIONS),
  [SEARCH_FIELD_NAMES.flagged]: Object.values(FLAGGED_OPTIONS),
  [SEARCH_FIELD_NAMES.inOut]: Object.values(IN_OUT_OPTIONS),
};

export const INBOX_SEARCH_FIELD_INITIAL_VALUE = {
  [SEARCH_FIELD_NAMES.searchText]: "",
  [SEARCH_FIELD_NAMES.searchIn]: `${SEARCH_IN_OPTIONS.subject.value},${SEARCH_IN_OPTIONS.body.value}`,
  [SEARCH_FIELD_NAMES.inOut]: IN_OUT_OPTIONS.notSet.value,
  [SEARCH_FIELD_NAMES.from]: "",
  [SEARCH_FIELD_NAMES.to]: "",
  [SEARCH_FIELD_NAMES.datePeriod]: DATE_PERIOD_OPTIONS.oneMonth.value,
  [SEARCH_FIELD_NAMES.flagged]: FLAGGED_OPTIONS.notSet.value,
  [SEARCH_FIELD_NAMES.hasAttachments]: HAS_ATTACHMENTS_OPTIONS.notSet.value,
  [SEARCH_FIELD_NAMES.readUnread]: READ_UNREAD_OPTIONS.notSet.value,
};

export const INBOX_SEARCH_FIELD_CLEAR_VALUE = {
  [SEARCH_FIELD_NAMES.searchText]: "",
  [SEARCH_FIELD_NAMES.searchIn]: "",
  [SEARCH_FIELD_NAMES.inOut]: IN_OUT_OPTIONS.notSet.value,
  [SEARCH_FIELD_NAMES.from]: "",
  [SEARCH_FIELD_NAMES.to]: "",
  [SEARCH_FIELD_NAMES.datePeriod]: DATE_PERIOD_OPTIONS.notSet.value,
  [SEARCH_FIELD_NAMES.flagged]: FLAGGED_OPTIONS.notSet.value,
  [SEARCH_FIELD_NAMES.hasAttachments]: HAS_ATTACHMENTS_OPTIONS.notSet.value,
  [SEARCH_FIELD_NAMES.readUnread]: READ_UNREAD_OPTIONS.notSet.value,
};

export const hasSearchFilters = (searchFilters: any) => {
  // Check if searchFilters is null, undefined, or not an object
  if (!searchFilters || typeof searchFilters !== 'object') {
    return false;
  }

  // Check if it has any values
  return Object.values(searchFilters).filter(value =>
    value !== null && value !== undefined && value !== ''
  ).length > 0;
};
