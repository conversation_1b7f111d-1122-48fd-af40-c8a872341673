import xs from "xstream";
import sampleCombine from "xstream/extra/sampleCombine";

import {
  getSearchFolderCriteria,
  getSearchFolderCriteriaFailed,
  getSearchFolderCriteriaSuccess,
} from "../slices/criteriaSlice";
import { setSearchFiltersBasedOnSavedCriteria } from "../slices/searchFiltersSlice";
import { SEARCH_ENTITIES } from "../constants/searchEntities";

export const fetchSearchFolderCriteria = (sources) => {
  const state$ = sources.STATE;
  const token$ = state$.map((state) => state?.persist?.bTeamAuth?.token);
  const domainBaseUrl$ = state$.map(
    (state) => state?.persist?.bTeamAuth?.domainBaseUrl
  );

  const request$ = sources.ACTION.filter(
    (action) => action.type === getSearchFolderCriteria.type
  )
    .compose(sampleCombine(token$, domainBaseUrl$))
    .map(([action, token, domainBaseUrl]) => {
      return {
        url: `${domainBaseUrl}/api/Criteria?id=${action.payload.folderCriteriaId}`,
        category: "getSearchFolderCriteria",
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };
    });

  const response$ = sources.HTTP.select("getSearchFolderCriteria")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status === 200);

  const action1$ = response$.map((response) => {
    return getSearchFolderCriteriaSuccess({ responseBody: response?.body });
  });

  const action2$ = response$.map((response) => {
    return setSearchFiltersBasedOnSavedCriteria({
      entityType: SEARCH_ENTITIES.messages,
      responseBody: response?.body,
    });
  });

  return {
    ACTION: xs.merge(action1$, action2$),
    HTTP: request$,
  };
};

export const fetchSearchFolderCriteriaFailed = (sources) => {
  const response$ = sources.HTTP.select("getSearchFolderCriteria")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status !== 200);

  const action$ = response$.map((response) =>
    getSearchFolderCriteriaFailed(response)
  );

  return {
    ACTION: action$,
  };
};
