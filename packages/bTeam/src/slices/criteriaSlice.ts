import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { CriterionDTO } from "../types/DTOs/CriterionDTO";
import { Criterion } from "../types/criterion";
import { mapCriterion } from "../helpers/mapCriterion";

// Initial state
interface CriteriaState {
  getSearchFolderCriteriaIsLoading: boolean;
  getSearchFolderCriteriaErrorMessage: string;
  selectedMessageCriterionId: string;
  criteria: {
    byId: { [id: string]: Criterion };
    allIds: string[];
  };
}

const initialState: CriteriaState = {
  getSearchFolderCriteriaIsLoading: false,
  getSearchFolderCriteriaErrorMessage: "",
  selectedMessageCriterionId: "",
  criteria: {
    byId: {},
    allIds: [],
  },
};

// Slice
export const criteriaSlice = createSlice({
  name: "bTeamCriteriaSlice",
  initialState,
  reducers: {
    getSearchFolderCriteria(
      state,
      action: PayloadAction<{ folderCriteriaId: string }>
    ) {
      state.getSearchFolderCriteriaIsLoading = true;
      state.getSearchFolderCriteriaErrorMessage = "";
      state.selectedMessageCriterionId = "";
    },
    getSearchFolderCriteriaSuccess(
      state,
      action: PayloadAction<{ responseBody: CriterionDTO }>
    ) {
      const criteriaData = action.payload.responseBody;
      const mappedCriterion = mapCriterion(criteriaData);

      state.getSearchFolderCriteriaIsLoading = false;
      state.getSearchFolderCriteriaErrorMessage = "";
      state.selectedMessageCriterionId = mappedCriterion.id;
      state.criteria.byId[mappedCriterion.id] = mappedCriterion;
      state.criteria.allIds = Array.from(new Set([...state.criteria.allIds, mappedCriterion.id]));
    },
    getSearchFolderCriteriaFailed(state, action) {
      state.getSearchFolderCriteriaIsLoading = false;
      state.getSearchFolderCriteriaErrorMessage =
        "Error retrieving search folder criteria";
    },
  },
});

// Export actions
export const {
  getSearchFolderCriteria,
  getSearchFolderCriteriaSuccess,
  getSearchFolderCriteriaFailed,
} = criteriaSlice.actions;

// Export reducer
export default criteriaSlice.reducer;
