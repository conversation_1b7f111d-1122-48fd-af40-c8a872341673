import { Folder } from "../types/folder";
import { CriterionDataDTO, CriterionDTO } from "../types/DTOs/CriterionDTO";
import {SEARCH_RESULTS_FOLDER_ID} from "../constants/searchResultsFolder";
import {Criterion} from "../types/criterion";

export const isSearchFolderSavedCriteria = (folder: Folder) =>
  Boolean(folder?.folderCriteriaId);

// We check if the current selected folder is a saved criteria.
// If yes we set selected folder id to navigate the search results folder
export const checkForCriteriaFolderAndNavigateToSearchResultsFolder = (
  selectedFolder: Folder,
  setSelectedFolderAction: (folderId: string) => void,
  createSearchResultsFolderAction: (payload: {
    searchResultsFolderIds?: string[];
  }) => void,
  selectedMessageCriterionId: string,
  criteriaById: Record<string, Criterion>,
) => {
  if (isSearchFolderSavedCriteria(selectedFolder)) {
    setSelectedFolderAction(SEARCH_RESULTS_FOLDER_ID);
    createSearchResultsFolderAction({
      searchResultsFolderIds: criteriaById?.[selectedMessageCriterionId]?.data?.folderIds,
    });
  }
};

/**
 * Type for a processed criteria with the CRC_Data already parsed
 */
export type ProcessedCriteriaDTO = Omit<CriterionDTO, "CRC_Data"> & {
  CRC_Data: CriterionDataDTO;
};

/**
 * Helper function to parse the CRC_Data string into an object
 */
export const parseCriteriaData = (
  criteria: CriterionDTO
): ProcessedCriteriaDTO => {
  try {
    return {
      ...criteria,
      CRC_Data: JSON.parse(criteria.CRC_Data) as CriterionDataDTO,
    };
  } catch (error) {
    console.error("Error parsing criteria data:", error);
    return {
      ...criteria,
      CRC_Data: {} as CriterionDataDTO,
    };
  }
};
