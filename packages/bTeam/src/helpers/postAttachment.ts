import env from "react-native-config";
import { Alert } from "react-native";
import DocumentPicker from "react-native-document-picker";

const MAX_ATTACHMENT_SIZE = 8 * 1024 * 1024; // 8MB in bytes
const RESTRICTED_FILE_TYPES = ["exe", "com", "bat"]; // List of restricted file extensions

/**
 * Helper function to upload an attachment.
 * @param {string} token - Authentication token.
 * @param {string} uploadId - Unique upload identifier.
 * @param {number} FLN_ENT_Id - Type identifier (1001 - message, 1006, comment).
 * @param {number} currentTotalSize - Current total size of selected attachments in bytes.
 * @param {(FLN_Guid: string, documentSize: number) => void} onSuccess - Callback function to update state with the uploaded file ID.
 * @param {(error: string) => void} onError - Callback function to handle errors.
 * @param {string} domainBaseUrl - Base URL for the API.
 * @param {() => void} onCancel - Optional callback function called when user cancels the document picker.
 */
export const uploadAttachmentHelper = async (
  token,
  uploadId,
  FLN_ENT_Id,
  currentTotalSize,
  onSuccess,
  onError,
  domainBaseUrl,
  onCancel
) => {
  try {
    // Open Document Picker
    const res = await DocumentPicker.pick({
      type: [DocumentPicker.types.allFiles],
    });

    const document = res[0];

    // Get the file extension
    const fileExtension = document.name.split(".").pop().toLowerCase();

    // Check if the file type is restricted
    if (RESTRICTED_FILE_TYPES.includes(fileExtension)) {
      Alert.alert("File type not allowed", "You cannot upload this file type.");
      throw new Error("Restricted file type");
    }

    // Check if adding this file exceeds the 8MB limit
    if (currentTotalSize + document.size > MAX_ATTACHMENT_SIZE) {
      Alert.alert(
        "Attachment Limit Exceeded",
        "Adding this file would exceed the 8MB total limit."
      );
      throw new Error("Attachment Limit Exceeded");
    }

    const formData = new FormData();
    formData.append("file", {
      uri: document.uri,
      name: document.name,
      type: document.type,
    });

    const response = await fetch(
      `${domainBaseUrl}/api/FileLinkFiles?FLN_EntityPK_Guid_Temp=${uploadId}&FLN_ENT_Id=${FLN_ENT_Id}&setPermanent=false`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: "application/json",
        },
        body: formData,
      }
    );

    const result = await response.json();

    if (result && result.FLN_Guid) {
      onSuccess && onSuccess(result.FLN_Guid, document.size, document.name);
    } else {
      throw new Error("Invalid response from server");
    }
  } catch (err) {
    if (DocumentPicker.isCancel(err)) {
      onCancel && onCancel();
    } else {
      const errorMessage = err.message || "Upload failed";
      Alert.alert("Error", errorMessage);
      onError && onError(errorMessage);
      console.error("Error picking document:", err);
    }
  }
};
