import React, { cloneElement, useRef } from "react";
import { StyleSheet, TextInput, View, Platform, LayoutChangeEvent } from "react-native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from "react-native-reanimated";
import { type Theme, useThemeAwareObject } from "b-ui-lib";

// Constants (can be overridden via props)
const DEFAULT_HEADER_HEIGHT = Platform.OS === 'ios' ? 107 : 107;
const DEFAULT_BUFFER = 10;
const DEFAULT_ANIMATION_STARTING_POINT = 30;

type Props = {
  headerHeight?: number;
  buffer?: number;
  animationStart?: number;
  searchBar?: React.ReactElement;
  children: React.ReactElement;
};

const SearchBarAnimationHOC = ({
  headerHeight = DEFAULT_HEADER_HEIGHT,
  buffer = DEFAULT_BUFFER,
  animationStart = DEFAULT_ANIMATION_STARTING_POINT,
  searchBar,
  children,
}: Props) => {
  const searchInputRef = useRef<TextInput>(null);
  const { color } = useThemeAwareObject(createStyles);

  // Animation Start

  // Shared values
  const translateY = useSharedValue(0);
  const prevScrollY = useSharedValue(0);
  const isHidden = useSharedValue(false);
  const inboxListInitialHeight = useSharedValue(0);
  const inboxListHeight = useSharedValue(0);

  const blurSearchInput = () => {
    searchInputRef.current?.blur();
  };

  // Regular scroll handler function that components can use
  const scrollHandler = (event: any) => {
    // Blur searchInput onScroll list
    blurSearchInput();

    const currentY = event.nativeEvent?.contentOffset?.y || 0;
    const scrollDiff = currentY - prevScrollY.value;

    if (scrollDiff < -buffer && isHidden.value) {
      // Scrolling UP & Header is hidden → Show it
      translateY.value = withTiming(0, { duration: 300 });
      isHidden.value = false;
      inboxListHeight.value = withTiming(inboxListInitialHeight.value, {
        duration: 300,
      });
    } else if (
      scrollDiff > buffer &&
      !isHidden.value &&
      currentY > headerHeight
    ) {
      // Scrolling DOWN & Header is visible → Hide it
      translateY.value = withTiming(-headerHeight, { duration: 300 });
      isHidden.value = true;
      inboxListHeight.value = withTiming(
        inboxListInitialHeight.value + headerHeight,
        { duration: 300 }
      );
    }

    // Before animation start point, reset to initial state
    if (currentY < animationStart) {
      translateY.value = 0;
      isHidden.value = false;
      inboxListHeight.value = withTiming(inboxListInitialHeight.value, {
        duration: 300,
      });
    }

    prevScrollY.value = currentY;
  };

  // Animated styles
  const headerAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
  }));

  const listAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
    minHeight: inboxListHeight.value,
  }));

  // Layout handler
  const handleLayout = (event: LayoutChangeEvent) => {
    const { height } = event.nativeEvent.layout;

    if (inboxListInitialHeight.value === 0) {
      inboxListInitialHeight.value = height;
    }
  };
  // Animation End

  // Prepare props to pass to child component
  const childProps: any = {
    onScroll: scrollHandler,
    onLayout: handleLayout,
  };

  // Handle style prop - use 'style' for native components, 'listStyle' for custom components
  if (children.props.listStyle !== undefined) {
    // Custom component that expects listStyle
    childProps.listStyle = [
      ...(Array.isArray(children.props.listStyle)
        ? children.props.listStyle
        : [children.props.listStyle]),
      listAnimatedStyle,
    ];
  } else {
    // Native component that expects style - don't pass animated styles to avoid conflicts
    childProps.style = children.props.style;
  }

  return (
    <View style={{ flex: 1 }}>
      {searchBar && (
        <Animated.View
          style={[
            {
              height: headerHeight,
              backgroundColor: color.BACKGROUND,
              zIndex: 1000,
            },
            headerAnimatedStyle,
          ]}
        >
          {cloneElement(searchBar, {
            searchInputRef: searchInputRef,
          })}
        </Animated.View>
      )}

      <Animated.View style={[{ flex: 1 }, listAnimatedStyle]}>
        {cloneElement(children, childProps)}
      </Animated.View>
    </View>
  );
};

export default SearchBarAnimationHOC;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({});

  return { styles, color };
};
