import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  cloneElement,
  useMemo,
} from "react";
import { Alert } from "react-native";
import { useDispatch, useSelector } from "react-redux";
import uuid from "react-native-uuid";
import { GestureHandlerRootView } from "react-native-gesture-handler";

// Import CommentBottomSheet
import CommentBottomSheet, {
  CommentBottomSheetRef,
} from "../components/general/CommentBottomSheet";

// Import the comment-related actions and helpers
import {
  postMessageComment,
  postMessageCommentSuccessDismiss,
  starMessageComment,
  getMessageCommentUsers,
} from "../slices/generalSlice";
import { uploadAttachmentHelper } from "../helpers/postAttachment";

interface CommentsHOCProps {
  guid: string;
  entityId: string;
  umsGuidToSendComment: string;
  commentIds: string[];
  children: React.ReactElement<any>;
  refreshListCallback: () => void;
}

const CommentsHOC: React.FC<CommentsHOCProps> = ({
  guid,
  entityId,
  commentIds,
  umsGuidToSendComment,
  children,
  refreshListCallback,
}) => {
  const dispatch = useDispatch();
  const { comments } = useSelector((state) => state.persist.bTeamCommentsSlice);
  
  // Bottom sheet ref for comments
  const bottomSheetRef = useRef<CommentBottomSheetRef>(null);

  const {
    commentMessageUsers,
    getMessageCommentUsersLoading,
    getMessageCommentUsersError,
    postMessageCommentLoading,
    postMessageCommentError,
    postMessageCommentSuccess,
  } = useSelector((state: any) => state.root.bTeamGeneralSlice);

  // Create message-specific comment data hash to optimize memoization
  const relevantCommentData = useMemo(() => {
    if (!commentIds || commentIds.length === 0) return {};

    const result: Record<string, any> = {};
    commentIds.forEach((id: string) => {
      if (comments.byId[id]) {
        result[id] = comments.byId[id];
      }
    });
    return result;
  }, [commentIds, comments.byId]);

  const { participantUsers } = useSelector(
    (state: any) => state.root.bTeamGeneralNotificationSlice
  );
  // Local state for attachment uploads within comments
  const [uploadAttachmentLoading, setUploadAttachmentLoading] = useState(false);
  const [uploadAttachmentError, setUploadAttachmentError] = useState("");
  const [commentAttachments, setCommentAttachments] = useState<string[]>([]);
  const [totalAttachmentSize, setTotalAttachmentSize] = useState(0);

  // Create a unique file upload ID on mount.
  const FILE_UPLOAD_ID = useRef<string | null>(null);
  useEffect(() => {
    FILE_UPLOAD_ID.current = uuid.v4() as string;
  }, []);

  const fetchCommentNotifiedUsers = (commentGuid: string) => {
    dispatch(
      getMessageCommentUsers({
        guid: commentGuid,
        entityId: 1006,
      })
    );
  };

  // Compute the effective UMS_Guid based on the entity type.
  const effectiveUMSGuid =
    entityId === "1001" ? umsGuidToSendComment || guid : guid;

  // Function to open the new comment UI
  const handleOpenNewComment = useCallback(() => {
    bottomSheetRef.current?.openForNewComment();
  }, []);

  // Function to handle reply to comment
  const handleReplyToComment = useCallback((commentId: string) => {
    bottomSheetRef.current?.openForReply(commentId);
  }, []);

  // Function to post a new comment.
  const handlePostNewComment = useCallback(
    ({
      CMM_CMM_Guid,
      notifyUsers,
      description,
      restricted,
    }: {
      CMM_CMM_Guid: string | null; // Allow null for memo-type new comments
      notifyUsers: string[];
      description: string;
      restricted: 0 | 1;
    }) => {
      if (!notifyUsers || notifyUsers.length === 0) {
        Alert.alert(
          "No Recipients Selected",
          "Please select at least one recipient before posting your comment."
        );
        return;
      }

      dispatch(
        postMessageComment({
          UMS_Guid: effectiveUMSGuid,
          CMM_CMM_Guid: CMM_CMM_Guid!,
          CMM_ENT_Id: entityId,
          notifyUsers,
          description,
          restricted,
          commentAttachments,
          FLN_EntityPK_Guid_Temp: FILE_UPLOAD_ID.current,
        })
      );

      // Trigger a refresh of comments after posting.
      setTimeout(() => {
        refreshListCallback();
      }, 1);
    },
    [
      commentAttachments,
      dispatch,
      effectiveUMSGuid,
      entityId,
      refreshListCallback,
    ]
  );

  const handlePostMessageCommentSuccessDismiss = useCallback(() => {
    setCommentAttachments([]);
    dispatch(postMessageCommentSuccessDismiss());
  }, [dispatch]);

  const handleStarComment = useCallback(
    (id: string) => {
      const newStarValue = !comments.byId[id].isStarred;

      // Single action handles both optimistic update and API call
      dispatch(
        starMessageComment({
          id,
          value: newStarValue,
          guid: effectiveUMSGuid,
          entityId,
        })
      );

      setTimeout(() => {
        refreshListCallback();
      }, 1);
    },
    [dispatch, effectiveUMSGuid, comments.byId, refreshListCallback]
  );

  const { token, domainBaseUrl } = useSelector(
    (state: any) => state.persist.bTeamAuth
  );
  const handleAddAttachment = useCallback(async () => {
    setUploadAttachmentLoading(true);
    setUploadAttachmentError("");

    await uploadAttachmentHelper(
      token,
      FILE_UPLOAD_ID.current!,
      1006,
      totalAttachmentSize,
      (FLN_Guid: string, documentSize: number) => {
        setCommentAttachments((prev) => [...prev, FLN_Guid]);
        setTotalAttachmentSize((prev) => prev + documentSize);
        setUploadAttachmentLoading(false);
      },
      (error: string) => {
        setUploadAttachmentLoading(false);
        setUploadAttachmentError(error);
      },
      domainBaseUrl
    );
  }, [token, totalAttachmentSize]);

  // Memoize mapped comments with message-specific dependencies to prevent excessive re-renders
  const mappedComments = useMemo(() => {
    if (!commentIds || commentIds.length === 0) return [];

    const mapCommentReplies = (commentId: string) => {
      const comment = relevantCommentData[commentId];
      if (!comment) return null;

      return {
        ...comment,
        replies: commentIds
          ?.filter((id) => relevantCommentData[id]?.parentId === comment.id)
          .map(mapCommentReplies)
          .filter(Boolean), // Remove null entries
      };
    };

    return (
      commentIds
        ?.filter((id) => relevantCommentData[id]?.parentId === null)
        ?.map(mapCommentReplies)
        .filter(Boolean) || [] // Remove null entries and provide fallback
    );
  }, [
    commentIds,
    relevantCommentData, // Only this message's comment data
    guid, // Message GUID to ensure uniqueness
  ]);

  // Memoize the mapped participant users to avoid recalculation when data hasn't changed
  const mappedParticipantUsers = useMemo(() => {
    return participantUsers.allIds.map((userId: string, index: number) => ({
      id: participantUsers.byId[userId].id,
      value: participantUsers.byId[userId].username,
      avatarName: participantUsers.byId[userId].userAbbreviation
        .charAt(0)
        .toUpperCase(),
      avatarBackgroundColor: index % 2 === 0 ? "#FFB7CD" : "#FF8833",
    }));
  }, [participantUsers.allIds, participantUsers.byId]);

  // Memoize the comments props to avoid recreating the object unnecessarily
  const commentsProps = useMemo(
    () => ({
      comments: mappedComments,
      recipientsEmails: mappedParticipantUsers,
      notifiedUsers: commentMessageUsers,
      postMessageCommentLoading,
      postMessageCommentError,
      postMessageCommentSuccess,
      postNewComment: handlePostNewComment,
      onPressNewComment: handleOpenNewComment,
      onPressCommentReply: handleReplyToComment,
      handlePostMessageCommentSuccessDismiss,
      handleStarComment,
      handleAddAttachment,
      uploadAttachmentLoading,
      uploadAttachmentError,
      attachmentsLength: commentAttachments.length,
      fetchNotifiedUsers: fetchCommentNotifiedUsers,
      fetchNotifiedUsersLoading: getMessageCommentUsersLoading,
      fetchNotifiedUsersError: getMessageCommentUsersError,
    }),
    [
      mappedComments,
      mappedParticipantUsers,
      commentMessageUsers,
      postMessageCommentLoading,
      postMessageCommentError,
      postMessageCommentSuccess,
      handlePostNewComment,
      handleOpenNewComment,
      handleReplyToComment,
      handlePostMessageCommentSuccessDismiss,
      handleStarComment,
      handleAddAttachment,
      uploadAttachmentLoading,
      uploadAttachmentError,
      commentAttachments.length,
      fetchCommentNotifiedUsers,
      getMessageCommentUsersLoading,
      getMessageCommentUsersError,
    ]
  );

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      {cloneElement(children, { ...commentsProps })}
      <CommentBottomSheet
        ref={bottomSheetRef}
        recipientsEmails={mappedParticipantUsers}
        handlePostNewComment={handlePostNewComment}
        postMessageCommentLoading={postMessageCommentLoading}
        postMessageCommentError={postMessageCommentError}
        postMessageCommentSuccess={postMessageCommentSuccess}
        handlePostMessageCommentSuccessDismiss={handlePostMessageCommentSuccessDismiss}
        handleAddAttachment={handleAddAttachment}
        uploadAttachmentLoading={uploadAttachmentLoading}
        uploadAttachmentError={uploadAttachmentError}
        attachmentsLength={commentAttachments.length}
      />
    </GestureHandlerRootView>
  );
};

export default CommentsHOC;
