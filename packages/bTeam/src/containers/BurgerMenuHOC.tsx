import React, { useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  getFolderChildren,
  getFoldersCount,
  getFoldersUserSettings,
  setSelectedFolder,
  toggleFolderExpand,
} from "../slices/gridMessageSlice";
import { cancelMultiSelection } from "../slices/generalSlice";
import { createChildrenHierarchy } from "../helpers/createChildrenHierarchy";
import { clearSearchFields } from "../slices/searchFiltersSlice";
import { SEARCH_ENTITIES } from "../constants/searchEntities";

// Components
import BurgerMenuScreen from "../components/burgerMenu/BurgerMenuScreen";

type Props = {};

const BurgerMenuHOC: React.FC = ({}: Props) => {
  const [isBurgerMenuOpen, setIsBurgerMenuOpen] = useState<boolean>(false);

  const dispatch = useDispatch();
  const { folders, selectedFolderId } = useSelector(
    (state) => state.persist.gridMessageSlice
  );
  const { userName } = useSelector((state) => state.persist.bTeamAuth);

  const setSelectedFolderAction = (folderId: string) =>
    dispatch(setSelectedFolder(folderId));

  const cancelMultiSelectionAction = () => dispatch(cancelMultiSelection());

  const clearInboxSearchFieldsAction = () => {
    dispatch(clearSearchFields({ entityType: SEARCH_ENTITIES.messages }));
  };

  const getFolderChildrenAction = (folderId: string) => {
    dispatch(getFolderChildren({ folderId }));
  };

  const toggleFolderExpandAction = (folderId: string) => {
    dispatch(toggleFolderExpand(folderId));
  };

  const getFoldersCountAction = () => {
    dispatch(getFoldersCount());
  };

  const getFoldersUserSettingsAction = () => {
    dispatch(getFoldersUserSettings());
  };

  // When burger menu animation completes fetch folders counters
  const handleAnimationComplete = () => {
    getFoldersCountAction();
    getFoldersUserSettingsAction();
  };

  const openBurgerMenu = () => setIsBurgerMenuOpen(true);
  const closeBurgerMenu = () => setIsBurgerMenuOpen(false);

  const handleToggleFolderExpand = (folderId: string) => {
    if (
      !folders.byId?.[folderId].isExpanded &&
      !folders.byId?.[folderId]?.areChildrenFetched
    ) {
      getFolderChildrenAction(folderId);
    }

    toggleFolderExpandAction(folderId);
  };

  const foldersHierarchy = useMemo(
    () => createChildrenHierarchy(folders.byId, folders.rootIds, userName),
    [folders]
  );

  const onSelectFolder = (folderId: string) => {
    setSelectedFolderAction(folderId);
    cancelMultiSelectionAction();
    clearInboxSearchFieldsAction();
    closeBurgerMenu();
  };

  return (
    <BurgerMenuScreen
      isBurgerMenuOpen={isBurgerMenuOpen}
      openBurgerMenu={openBurgerMenu}
      closeBurgerMenu={closeBurgerMenu}
      folders={foldersHierarchy}
      handleToggleFolderExpand={handleToggleFolderExpand}
      onSelectFolder={onSelectFolder}
      selectedFolderId={selectedFolderId}
      handleFolderPressSearchItem={onSelectFolder}
      onAnimationComplete={handleAnimationComplete}
    />
  );
};

export default BurgerMenuHOC;
