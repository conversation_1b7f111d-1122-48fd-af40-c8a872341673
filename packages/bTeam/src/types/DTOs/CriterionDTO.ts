/**
 * DTO for the Criteria API response
 */
export type CriterionDTO = {
  CRC_CreatedUserGuid: string;
  CRC_CriteriaId: string;
  CRC_Data: string; // JSON string that needs to be parsed
  CRC_Description: string | null;
  CRC_Guid: string;
  CRC_IsDefault: boolean;
  CRC_Name: string;
  CRC_USR_Guid: string;
  CRC_UpdatedUserGuid: string;
  ContactCombos: any[]; // replace with specific type if available
  FLD_FLD_Guid: string | null;
  FLD_Names: string;
  entityId: string | null;
};

/**
 * DTO for the parsed CRC_Data field in the CriteriaDTO
 */
export type CriterionDataDTO = {
  FLD_Guids_String: string;
  MSS_MST_Id: string | null;
  MSS_TextValue: string | null;
  MSS_TextIn: string[];
  MSS_InOut: string;
  MSS_InOutUser: string | null;
  MSS_State: string | null;
  MSS_From: string | null;
  MSS_To: string | null;
  MSS_Key: string | null;
  MSS_FindRelated: string | null;
  MSS_TimePeriod: string;
  MSS_DateTimeFrom: string | null;
  MSS_DateTimeTo: string | null;
  MSS_Attachments: string;
  MSS_AttachmentFileName: string | null;
  MSS_AttachmentType: string | null;
  MSS_Read: string | null;
  MSS_CategoriesBits: string | null;
  MSS_Important: string | null;
  MSS_Flagged: string;
  MSS_LinkedCase: string | null;
  MSS_MetaDataValues: string | null;
  MetaDataValuesSearchOperator: string | null;
  MSS_MetadataLinkedCases: string | null;
  FLD_Guid: string | null;
};
