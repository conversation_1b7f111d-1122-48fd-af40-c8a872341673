import {AttachmentDTO} from "./AttachmentDTO";

export type EmailAddress = {
  DisplayName: string | null;
  EmailAddress: string;
};

export type DraftMessageDTO = {
  UMS_Guid: string;
  UMS_CategoriesBits: number | null;
  UMS_MSF_Id: number | null;
  umsIsDeleted: boolean;
  umsIsViewed: boolean;
  MSG_Key: number;
  MSG_InOut: number;
  MSG_Guid: string;
  MSG_MST_Id: number;
  MSG_From: string;
  MSG_From_Parent: string | null;
  From: EmailAddress;
  MSG_To: string | null;
  MSG_To_Parent: string | null;
  Tos: EmailAddress[] | null;
  MSG_Cc: string | null;
  MSG_Cc_Parent: string | null;
  Ccs: EmailAddress[] | null;
  MSG_Bcc: string | null;
  Bccs: EmailAddress[] | null;
  MSG_Subject: string | null;
  MSG_Subject_Parent: string | null;
  MSG_Body: string;
  fullMessageBody?: string; // Added to support complete message body with preserved line breaks
  MSG_DateTime: string; // ISO 8601 datetime string
  MSG_DateTime_Parent: string | null;
  MSG_Importance: number;
  MSG_AttachmentsCount: number;
  MSG_AttachmentsNotEmbeddedImagesCount: number;
  MSG_AttachmentsGuid: string;
  MSG_RequestDeliveryReceipt: boolean;
  MSG_RequestReadReceipt: boolean;
  MSG_TimeForwarded: string | null;
  MSG_TimeReplied: string | null;
  MSG_TimeFilled: string | null;
  MSG_IsHtml: boolean;
  onHold: boolean;
  UNI_Guid: string;
  UNI_EmailAddress: string | null;
  UUN_SignatureText: string | null;
  Attachments: AttachmentDTO[] | null;
  MetadataEntityValuesString: string[];
  HasMetadata: boolean;
  HasCases: boolean;
  Cases: string[];
  CommentsCount: number;
  RemindersCount: number;
  UserFollowMessageActions: string | null;
  isDummy: boolean;
  MSG_SendMailingLists: string | null;
  mailingLists: string[];
  MSG_SendMailingListsMethod: number;
  MSG_SendMailingListsToCcBcc: number;
  MSG_TimeDelaySending: string | null;
  metadataCommentInfoBits: number;
  umsGuidChild: string | null;
  repliedByUser: string;
  msgTimeDelaySendingInterval: string;
  msgTimeDelaySendingValue: number;
  msgFromHasExistingContacting: boolean;
  FolderMessageInfo: string | null;
  isCalendarMeetingRequest: boolean;
  CalendarMeetingRequest: string | null;
};
