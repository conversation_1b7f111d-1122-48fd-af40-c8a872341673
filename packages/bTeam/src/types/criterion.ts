export type Criterion = {
  createdUserGuid: string;
  id: string; // from CRC_Guid
  criteriaId: string; // from CRC_CriteriaId
  data: CriterionData; // JSON string that needs to be parsed
  description: string | null;
  isDefault: boolean;
  name: string;
  usrGuid: string; // from CRC_USR_Guid
  updatedUserGuid: string;
  contactCombos: any[]; // replace with specific type if available
  fldGuid: string | null; // from FLD_FLD_Guid
  fldNames: string; // from FLD_Names
  entityId: string | null;
};

export type CriterionData = {
  folderIds: string[]; // from FLD_Guids_String - converted from comma-separated string to array
  mstId: string | null; // from MSS_MST_Id
  textValue: string | null; // from MSS_TextValue
  textIn: string[]; // from MSS_TextIn
  inOut: string; // from MSS_InOut
  inOutUser: string | null; // from MSS_InOutUser
  state: string | null; // from MSS_State
  from: string | null; // from MSS_From
  to: string | null; // from MSS_To
  key: string | null; // from MSS_Key
  findRelated: string | null; // from MSS_FindRelated
  timePeriod: string; // from MSS_TimePeriod
  dateTimeFrom: string | null; // from MSS_DateTimeFrom
  dateTimeTo: string | null; // from MSS_DateTimeTo
  attachments: string; // from MSS_Attachments
  attachmentFileName: string | null; // from MSS_AttachmentFileName
  attachmentType: string | null; // from MSS_AttachmentType
  read: string | null; // from MSS_Read
  categoriesBits: string | null; // from MSS_CategoriesBits
  important: string | null; // from MSS_Important
  flagged: string; // from MSS_Flagged
  linkedCase: string | null; // from MSS_LinkedCase
  metaDataValues: string | null; // from MSS_MetaDataValues
  metaDataValuesSearchOperator: string | null; // from MetaDataValuesSearchOperator
  metadataLinkedCases: string | null; // from MSS_MetadataLinkedCases
  folderGuid: string | null; // from FLD_Guid
};
