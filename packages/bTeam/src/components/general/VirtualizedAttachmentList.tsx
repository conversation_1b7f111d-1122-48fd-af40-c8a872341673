import React, { useMemo, useCallback } from "react";
import {
  FlatList,
  RefreshControl,
  StyleSheet,
  View,
  ListRenderItem,
} from "react-native";
import { Theme, useThemeAwareObject, SPACING, AttachmentItem } from "b-ui-lib";

type AttachmentData = {
  id: string;
  name: string;
  size: number;
  type: string;
  isLoading: boolean;
  [key: string]: any;
};

type Props = {
  attachments: AttachmentData[];
  handleDownload: (id: string) => void;
  refreshControl?: React.ReactElement<RefreshControl>;
};

const ITEM_HEIGHT = 70; // Estimated height of each attachment item

const VirtualizedAttachmentList: React.FC<Props> = ({
  attachments,
  handleDownload,
  refreshControl,
}) => {
  const { styles } = useThemeAwareObject(createStyles);

  // Memoize the data to prevent unnecessary re-renders
  const memoizedAttachments = useMemo(() => attachments, [attachments]);

  const renderAttachmentItem: ListRenderItem<AttachmentData> = useCallback(
    ({ item, index }) => (
      <View style={styles.itemContainer}>
        <AttachmentItem
          attachment={item}
          handleDownload={() => handleDownload(item.id)}
        />
      </View>
    ),
    [handleDownload, styles.itemContainer]
  );

  const keyExtractor = useCallback(
    (item: AttachmentData, index: number) => item.id || index.toString(),
    []
  );

  const getItemLayout = useCallback(
    (data: any, index: number) => ({
      length: ITEM_HEIGHT,
      offset: ITEM_HEIGHT * index,
      index,
    }),
    []
  );

  const ItemSeparator = useCallback(
    () => <View style={styles.separator} />,
    [styles.separator]
  );

  return (
    <FlatList
      data={memoizedAttachments}
      renderItem={renderAttachmentItem}
      keyExtractor={keyExtractor}
      getItemLayout={getItemLayout}
      ItemSeparatorComponent={ItemSeparator}
      refreshControl={refreshControl}
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      showsVerticalScrollIndicator={false}
      // Performance optimizations
      removeClippedSubviews={true}
      maxToRenderPerBatch={10}
      updateCellsBatchingPeriod={50}
      initialNumToRender={15}
      windowSize={21}
      // Enable view recycling for better memory management
      recyclerProps={{
        recyclerViewId: "attachment-list",
        enableViewRecycling: true,
      }}
    />
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({});

  return { styles, color };
};

export default VirtualizedAttachmentList;
