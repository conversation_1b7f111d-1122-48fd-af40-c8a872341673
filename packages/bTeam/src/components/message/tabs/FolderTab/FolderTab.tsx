import React, { useCallback } from "react";
import { RefreshControl, StyleSheet, View } from "react-native";
import { useFocusEffect } from "@react-navigation/native";
import {
  Theme,
  useThemeAwareObject,
  SPACING,
  MessageFolderList,
  TabTitle,
} from "b-ui-lib";

// Components
import EmptyTabBody from "../../../general/EmptyTabBody";
import TabErrorMessage from "../../TabErrorMessage";
import MessageBottomButtons from "../../../general/MessageBottomButtons";
import FolderTabSkeleton from "./FolderTabSkeleton";

type Props = {
  foldersIds: any;
  messageFolders: any;
  moveToFolder: () => void;
  copyToFolder: () => void;
  isLoading: boolean;
  handleRefreshList: () => void;
  copyOrMoveMessageFolderError: string;
  getMessageActionsError: string;
  handleClearCopyOrMoveMessageFolderError: () => void;
  width?: number;
};

const FolderTab: React.FC<Props> = ({
  foldersIds,
  messageFolders,
  moveToFolder,
  copyToFolder,
  isLoading,
  handleRefreshList,
  copyOrMoveMessageFolderError,
  getMessageActionsError,
  handleClearCopyOrMoveMessageFolderError,
  width = 400,
}) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  useFocusEffect(
    useCallback(() => {
      return () => handleClearCopyOrMoveMessageFolderError();
    }, [])
  );


  // Show skeleton while loading
  if (isLoading) {
    return <FolderTabSkeleton width={width} />;
  }

  return (
    <View style={styles.container}>
      <View style={styles.body}>
        <TabTitle title="Folders" count={foldersIds?.length || "0"} />

        {foldersIds && foldersIds?.length > 0 && (
          <MessageFolderList
            folders={foldersIds?.map(
              (folderId: string) => messageFolders?.byId?.[folderId]
            )}
            refreshControl={
              <RefreshControl
                refreshing={false}
                onRefresh={() => handleRefreshList()}
                tintColor={color.TEXT_DIMMED}
              />
            }
          />
        )}

        {!foldersIds?.length && (
          <EmptyTabBody
            iconName="folder"
            emptyMessage="Not included to any folder"
          />
        )}

        <TabErrorMessage
          text={getMessageActionsError || copyOrMoveMessageFolderError}
          isVisible={Boolean(
            getMessageActionsError || copyOrMoveMessageFolderError
          )}
        />
      </View>

    </View>
  );
};

export default React.memo(FolderTab);

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    body: {
      flex: 1,
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
      paddingTop: SPACING.M,
      paddingHorizontal: SPACING.M,
      gap: SPACING.SIX,
    },
    errorText: {
      paddingVertical: SPACING.TEN,
      color: color.ERROR,
    },
  });

  return { styles, color };
};
