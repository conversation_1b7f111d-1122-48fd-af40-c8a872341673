import React from "react";
import { StyleSheet, View } from "react-native";
import {
  type Theme,
  FONT_SIZES,
  useThemeAwareObject,
  MessageCommentList,
  SPACING,
  CustomText,
  MessageComment,
  Attachment,
  MessageInfoData,
  MessageInfoHeader,
  TabTitle,
} from "b-ui-lib";

import { NotificationMessageInfoData } from "../../types/notification";
import {
  NOTIFICATION_ENTITY_ICON,
  NOTIFICATIONS_ENTITY_IDS,
} from "../../constants/notificationsEntityIds";
import { getEmailsString } from "../../helpers/getEmailsString";

// Components
import MessageBottomButtons from "../general/MessageBottomButtons";
import { RelatedUsersList } from "../../types/userMails";

type Props = {
  title: string;
  entityId: string;
  dateTime: string;
  comments?: MessageComment[];
  attachments: { byId: { [id: string]: Attachment[] }; allIds: string[] };
  recipientsEmails?: RelatedUsersList;
  postMessageCommentLoading?: boolean;
  postMessageCommentError?: string;
  postMessageCommentSuccess?: boolean;
  handlePostMessageCommentSuccessDismiss?: () => void;
  handleStarComment?: () => void;
  handleAddAttachment?: () => void;
  uploadAttachmentLoading?: boolean;
  uploadAttachmentError?: string;
  postNewComment?: () => void;
  attachmentsLength?: number;
  handleDownloadAttachment: (attachmentId: string) => Promise<void>;
  handleDownloadAllAttachments: (attachmentIds: string[]) => Promise<void>;
  notificationMessageInfo: NotificationMessageInfoData;
  notificationMessageInfoIsLoading: boolean;
  notifiedUsers?: RelatedUsersList;
  fetchNotifiedUsers: () => void;
  fetchNotifiedUsersLoading: boolean;
  fetchNotifiedUsersError: string;
  onPressNewComment: () => void;
  onPressCommentReply: (commentId: string) => void;
};

const NotificationScreen = ({
  title,
  entityId,
  dateTime,
  notificationMessageInfo,
  notificationMessageInfoIsLoading,
  comments,
  attachments,
  recipientsEmails,
  notifiedUsers,
  postMessageCommentLoading,
  postMessageCommentError,
  postMessageCommentSuccess,
  postNewComment,
  handlePostMessageCommentSuccessDismiss,
  handleStarComment,
  handleAddAttachment,
  uploadAttachmentLoading,
  uploadAttachmentError,
  attachmentsLength,
  handleDownloadAttachment,
  handleDownloadAllAttachments,
  fetchNotifiedUsers,
  fetchNotifiedUsersLoading,
  fetchNotifiedUsersError,
  onPressNewComment,
  onPressCommentReply,
}: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);
  const commentsCount = comments?.length;

  // Create buttons array for "Add new comment"
  const BUTTONS = [
    {
      title: "Add new comment",
      isDisabled: false,
      onPress: onPressNewComment,
    },
  ];

  return (
    <View style={styles.container}>
      <View style={{ flex: 1, paddingHorizontal: SPACING.M }}>
        <MessageInfoHeader
          subject={title}
          sentDate={dateTime}
          notificationIcon={NOTIFICATION_ENTITY_ICON?.[entityId]}
          containerStyle={styles.messageInfoContainer}
        />

        <MessageInfoData
          isHidden={
            entityId !== NOTIFICATIONS_ENTITY_IDS.message ||
            (entityId === NOTIFICATIONS_ENTITY_IDS.message &&
              !notificationMessageInfoIsLoading &&
              !!notificationMessageInfo &&
              Object.keys(notificationMessageInfo)?.length === 0)
          }
          isSkeletonLoading={notificationMessageInfoIsLoading}
          skeletonBackgroundColor={color.MESSAGE_ITEM__BACKGROUND}
          avatarName={notificationMessageInfo?.avatarName}
          inOut={notificationMessageInfo?.inOut}
          username={notificationMessageInfo?.username}
          from={notificationMessageInfo?.from}
          tos={getEmailsString(notificationMessageInfo?.tos)}
          ccs={getEmailsString(notificationMessageInfo?.ccs)}
          bccs={getEmailsString(notificationMessageInfo?.bccs)}
          containerStyle={styles.messageInfoContainer}
        />

        <TabTitle
          title="Comments"
          count={commentsCount}
          containerStyles={styles.tabTitle}
        />

        {!!comments && comments.length > 0 ? (
          <MessageCommentList
            comments={comments}
            attachments={attachments}
            handlePressReply={onPressCommentReply}
            handlePressStar={handleStarComment}
            handleDownloadAttachment={handleDownloadAttachment}
            handleDownloadAllAttachments={handleDownloadAllAttachments}
            fetchNotifiedUsers={fetchNotifiedUsers}
            fetchNotifiedUsersLoading={fetchNotifiedUsersLoading}
            fetchNotifiedUsersError={fetchNotifiedUsersError}
            participantsList={notifiedUsers}
          />
        ) : (
          <CustomText style={styles.emptyText}>No Comments yet</CustomText>
        )}
      </View>

      <MessageBottomButtons buttons={BUTTONS} />
    </View>
  );
};

export default NotificationScreen;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
      gap: SPACING.SIX,
    },
    messageInfoContainer: {
      paddingVertical: SPACING.M,
    },
    tabTitle: {
      paddingTop: SPACING.M,
      marginVertical: 0,
    },
    buttonContainer: {
      backgroundColor: color.NOTIFICATION_BUTTON_BACKGROUND,
      marginVertical: SPACING.M,
    },
    buttonText: {
      color: color.NOTIFICATION_BUTTON_TEXT,
    },
    emptyText: {
      padding: SPACING.M,
      alignSelf: "center",
      fontSize: FONT_SIZES.TWELVE,
      color: color.HALF_DIMMED,
    },
  });

  return { styles, color };
};
