import React from "react";
import { Pressable, StyleSheet, View } from "react-native";
import {
  Theme,
  useThemeAwareObject,
  TabBarLabel,
  MessageListTab,
} from "b-ui-lib";
import { Message } from "../../../../types/message";
import { SCREEN_NAMES } from "../../../../constants/screenNames";
import { CaseDetailsData } from "../../../../types/CaseDetailsData";
import { RelatedUsersList } from "../../../../types/userMails";
import { Task } from "../../../../types/task";

// Components
import AttachmentTab from "../../../general/AttachmentTab";
import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs";
import CaseDetailsTab from "../caseDetailsTab";
import CommentListTab from "../../../general/CommentListTab";
import LinkedCasesTab from "../linkedCasesTab/LinkedCasesTab";
import TasksTab from "../TasksTab/TasksTab";

type Props = {
  caseData: CaseDetailsData;
  caseDateMessages: Message[];
  caseMessagesLoading: boolean;
  caseMessagesError: string;
  handleRefreshMessageList: () => void;
  comments: any;
  caseAttachmentsIds: any;
  attachments: any;
  attachmentsCount: number;
  downloadedAttachments: number;
  relevantUsers: RelatedUsersList;
  notifiedUsers?: RelatedUsersList;
  caseDetailsLoading: boolean;
  caseDetailsError: string;
  getCaseCommentsLoading: boolean;
  getCaseActionsLoading: boolean;
  getCaseCommentsError: string;
  getCaseActionsError: string;
  getMessageActions: () => void;
  clearDownloadedAttachments: () => void;
  handleClearCopyOrMoveMessageFolderError: () => void;
  postNewComment: () => void;
  postMessageCommentLoading: boolean;
  postMessageCommentError: string;
  postMessageCommentSuccess: boolean;
  handlePostMessageCommentSuccessDismiss: () => void;
  handleAddAttachment: () => void;
  uploadAttachmentLoading: boolean;
  uploadAttachmentError: string;
  handleStarComment: () => void;
  attachmentsLength: number;
  handleDownloadAttachment: () => void;
  handleDownloadAllAttachments: () => void;
  isAnyFileDownloadLoading: boolean;
  caseMetadata: any;
  caseLinkedCasesLoading: boolean;
  caseLinkedCasesError: string;
  handleRefreshLinkedCaseList: () => void;
  caseLinkedCases: CaseDetailsData[];
  handleTapCase?: (caseId: string) => void;
  tasks: Task[];
  caseTasksLoading: boolean;
  caseTasksError: string;
  handleRefreshTaskList: () => void;
  handleTapTask: (taskId: string) => void;
  fetchNotifiedUsers: () => void;
  fetchNotifiedUsersLoading: boolean;
  fetchNotifiedUsersError: string;
  onPressNewComment: () => void;
  onPressCommentReply: (commentId: string) => void;
};

function MyTabBar({ state, descriptors, navigation, position }) {
  const { color } = useThemeAwareObject(createStyles);

  return (
    <View style={{ flexDirection: "row", backgroundColor: "#61616B" }}>
      {state.routes.map((route, index) => {
        const { options } = descriptors[route.key];
        const label =
          options.tabBarLabel !== undefined
            ? options.tabBarLabel
            : options.title !== undefined
            ? options.title
            : route.name;

        const isFocused = state.index === index;

        const onPress = () => {
          const event = navigation.emit({
            type: "tabPress",
            target: route.key,
            canPreventDefault: true,
          });

          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name, route.params);
          }
        };

        const onLongPress = () => {
          navigation.emit({
            type: "tabLongPress",
            target: route.key,
          });
        };

        const inputRange = state.routes.map((_, i) => i);
        const opacity = position.interpolate({
          inputRange,
          outputRange: inputRange.map((i) => (i === index ? 1 : 0)),
        });

        return (
          <Pressable
            // activeOpacity={1}
            accessibilityRole="button"
            accessibilityState={isFocused ? { selected: true } : {}}
            accessibilityLabel={options.tabBarAccessibilityLabel}
            testID={options.tabBarTestID}
            onPress={onPress}
            onLongPress={onLongPress}
            style={{
              flex: 1,
              justifyContent: "center",
              alignItems: "center",
              height: 75,
              backgroundColor: isFocused
                ? color.MESSAGE_ITEM__BACKGROUND
                : color.BORDER_EMAIL_FOLDER,
              borderWidth: 1,
              borderRightColor: color.BORDER_EMAIL_FOLDER,
              borderLeftColor: color.BORDER_EMAIL_FOLDER,
              borderBottomColor: color.MESSAGE_ITEM__BACKGROUND,
              borderTopWidth: 3,
              borderTopColor: isFocused
                ? color.MESSAGE_FLAG
                : color.BORDER_EMAIL_FOLDER,
              elevation: 0,
              shadowOpacity: 0,
            }}
          >
            {label({ focused: isFocused, onPress })}
          </Pressable>
        );
      })}
    </View>
  );
}

const TabBarNavigator = ({
  caseData,
  caseDateMessages,
  caseMessagesLoading,
  caseMessagesError,
  handleRefreshMessageList,
  caseDetailsLoading,
  caseDetailsError,
  comments,
  caseAttachmentsIds,
  attachments,
  attachmentsCount,
  downloadedAttachments,
  relevantUsers,
  notifiedUsers,
  getMessageActions,
  clearDownloadedAttachments,
  handleClearCopyOrMoveMessageFolderError,
  postNewComment,
  postMessageCommentLoading,
  postMessageCommentError,
  postMessageCommentSuccess,
  handlePostMessageCommentSuccessDismiss,
  handleStarComment,
  attachmentsLength,
  handleDownloadAttachment,
  handleDownloadAllAttachments,
  isAnyFileDownloadLoading,
  caseMetadata,
  handleAddAttachment,
  uploadAttachmentLoading,
  uploadAttachmentError,
  caseLinkedCasesLoading,
  caseLinkedCasesError,
  handleRefreshLinkedCaseList,
  caseLinkedCases,
  handleTapCase,
  tasks,
  caseTasksLoading,
  caseTasksError,
  handleRefreshTaskList,
  handleTapTask,
  fetchNotifiedUsers,
  fetchNotifiedUsersLoading,
  fetchNotifiedUsersError,
  onPressNewComment,
  onPressCommentReply,
}: Props) => {
  const Tab = createMaterialTopTabNavigator();

  return (
    <Tab.Navigator
      tabBar={(props) => <MyTabBar {...props} />}
      initialRouteName="Html body"
    >
      <Tab.Screen
        name="Html body"
        options={{
          tabBarLabel: ({ focused, onPress }) => (
            <TabBarLabel
              iconName="briefcase"
              focused={focused}
              onPress={onPress}
            />
          ),
        }}
      >
        {() => (
          <CaseDetailsTab
            data={caseData}
            relevantUsers={relevantUsers}
            caseDetailsLoading={caseDetailsLoading}
            caseDetailsError={caseDetailsError}
            caseMetadata={caseMetadata}
          />
        )}
      </Tab.Screen>

      <Tab.Screen
        name="Task List"
        options={{
          tabBarLabel: ({ focused, onPress }) => (
            <TabBarLabel
              iconName="list-with-bullets"
              focused={focused}
              count={tasks?.length}
              onPress={onPress}
            />
          ),
        }}
      >
        {() => (
          <TasksTab
            tasks={tasks}
            handleTapTask={handleTapTask}
            handleRefreshList={handleRefreshTaskList}
            isLoading={caseTasksLoading}
            errorMessage={caseTasksError}
          />
        )}
      </Tab.Screen>

      <Tab.Screen
        name="Message List"
        options={{
          tabBarLabel: ({ focused, onPress }) => (
            <TabBarLabel
              iconName="mail"
              focused={focused}
              count={caseDateMessages?.length}
              onPress={onPress}
            />
          ),
        }}
      >
        {({ navigation }) => (
          <MessageListTab
            messages={caseDateMessages}
            handleTapMessage={(messageId: string) =>
              navigation.navigate(SCREEN_NAMES.message, { UMS_Guid: messageId })
            }
            isLoading={caseMessagesLoading}
            errorMessage={caseMessagesError}
            handleRefreshList={handleRefreshMessageList}
          />
        )}
      </Tab.Screen>

      <Tab.Screen
        name="Cases List"
        options={{
          tabBarLabel: ({ focused, onPress }) => (
            <TabBarLabel
              iconName="link"
              focused={focused}
              count={caseLinkedCases?.length}
              onPress={onPress}
            />
          ),
        }}
      >
        {() => (
          <LinkedCasesTab
            cases={caseLinkedCases}
            handleTapCase={handleTapCase}
            handleRefreshList={handleRefreshLinkedCaseList}
            isLoading={caseLinkedCasesLoading}
            errorMessage={caseLinkedCasesError}
          />
        )}
      </Tab.Screen>

      <Tab.Screen
        name="Comments List"
        options={{
          tabBarLabel: ({ focused, onPress }) => (
            <TabBarLabel
              iconName="message"
              focused={focused}
              count={comments?.length}
              onPress={onPress}
            />
          ),
        }}
      >
        {() => (
          <CommentListTab
            comments={comments}
            recipientsEmails={relevantUsers}
            handlePostNewComment={postNewComment}
            handleStarComment={handleStarComment}
            postMessageCommentLoading={postMessageCommentLoading}
            postMessageCommentError={postMessageCommentError}
            postMessageCommentSuccess={postMessageCommentSuccess}
            handlePostMessageCommentSuccessDismiss={
              handlePostMessageCommentSuccessDismiss
            }
            notifiedUsers={notifiedUsers}
            handleAddAttachment={handleAddAttachment}
            uploadAttachmentLoading={uploadAttachmentLoading}
            uploadAttachmentError={uploadAttachmentError}
            attachmentsLength={attachmentsLength}
            attachments={attachments}
            handleDownloadAttachment={handleDownloadAttachment}
            handleDownloadAllAttachments={handleDownloadAllAttachments}
            fetchNotifiedUsers={fetchNotifiedUsers}
            fetchNotifiedUsersLoading={fetchNotifiedUsersLoading}
            fetchNotifiedUsersError={fetchNotifiedUsersError}
            onPressNewComment={onPressNewComment}
            onPressReply={onPressCommentReply}
          />
        )}
      </Tab.Screen>

      <Tab.Screen
        name="Attachment List"
        options={{
          tabBarLabel: ({ focused, onPress }) => (
            <TabBarLabel
              iconName="paperclip"
              focused={focused}
              count={attachmentsCount?.toString()}
              onPress={onPress}
            />
          ),
        }}
      >
        {() => (
          <AttachmentTab
            attachmentsIds={caseAttachmentsIds}
            messageAttachments={attachments}
            attachmentsCount={attachmentsCount}
            downloadedAttachments={downloadedAttachments}
            handleRefreshList={getMessageActions}
            clearDownloadedAttachments={clearDownloadedAttachments}
            handleDownloadAttachment={handleDownloadAttachment}
            handleDownloadAllAttachments={handleDownloadAllAttachments}
            isAnyFileDownloadLoading={isAnyFileDownloadLoading}
          />
        )}
      </Tab.Screen>

      {/* <Tab.Screen
        name="Folder List"
        options={{
          tabBarLabel: ({ focused, onPress }) => (
            <TabBarLabel
              iconName="folder"
              focused={focused}
              count={foldersIds?.length}
              onPress={onPress}
            />
          ),
        }}
      >
        {() => (
          <FolderTab
            foldersIds={foldersIds}
            messageFolders={messageFolders}
            moveToFolder={moveToFolder}
            copyToFolder={copyToFolder}
            isLoading={getMessageActionsLoading}
            handleRefreshList={getMessageActions}
            copyOrMoveMessageFolderError={copyOrMoveMessageFolderError}
            getMessageActionsError={getMessageActionsError}
            handleClearCopyOrMoveMessageFolderError={
              handleClearCopyOrMoveMessageFolderError
            }
          />
        )}
      </Tab.Screen>

      <Tab.Screen
        name="Cases List"
        options={{
          tabBarLabel: ({ focused, onPress }) => (
            <TabBarLabel iconName="info" focused={focused} onPress={onPress} />
          ),
        }}
      >
        {() => <CaseTab caseMetadata={caseMetadata} cases={cases} />}
      </Tab.Screen> */}
    </Tab.Navigator>
  );
};
export default TabBarNavigator;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {},
  });

  return { styles, color };
};
