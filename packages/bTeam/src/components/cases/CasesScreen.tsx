import React from "react";
import { FlatList, RefreshControl, StyleSheet, View, ViewStyle, LayoutChangeEvent } from "react-native";
import {
  type Theme,
  useThemeAwareObject,
  CustomText,
  SPACING,
  CaseItem,
} from "b-ui-lib";
import { CaseDTO } from "../../types/DTOs/CasesListDTO";
import ContentLoader, { Rect, Circle } from "react-content-loader/native";
import Animated from "react-native-reanimated";

const AnimatedFlatList = Animated.createAnimatedComponent(
  FlatList<Notification>
);

type Props = {
  listData: CaseDTO[];
  handleRefreshList: () => void;
  isLoading: boolean;
  errorMessage?: string;
  listFooterComponent?: React.ReactElement;
  loadMore: () => void;
  handleTapCase: (caseId: string) => void;
  onLayout?: (event: LayoutChangeEvent) => void;
  onScroll?: any;
  listStyle?: ViewStyle | ViewStyle[];
};

const CasesScreen = ({
  listData,
  handleRefreshList,
  isLoading,
  errorMessage,
  listFooterComponent,
  loadMore,
  handleTapCase,
  onLayout,
  onScroll,
  listStyle,
}: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  if (isLoading) {
    return (
      <View
        style={{
          flex: 1,
          backgroundColor: color.BACKGROUND,
          paddingTop: 10,
          paddingHorizontal: 15,
        }}
      >
        {[...Array(3)].map((_, index) => (
          <ContentLoader
            width="100%"
            height={90}
            speed={1}
            backgroundColor={color.SKELETON_BACKGROUND}
            foregroundColor={color.SKELETON_FOREGROUND}
          >
            {/* Icon on the left */}
            <Circle cx="24" cy="40" r="16" />

            {/* Reference (e.g. "CAS-1662") */}
            <Rect x="60" y="20" rx="4" ry="4" width="60" height="12" />

            {/* Title (e.g. "Navios // LMS Requests and Certificates") */}
            <Rect x="60" y="38" rx="4" ry="4" width="200" height="12" />

            {/* Assigned to (e.g. "#Username#") */}
            <Rect x="60" y="56" rx="4" ry="4" width="120" height="12" />

            {/* Date on the right (e.g. "Sep 12, 12:23") */}
            <Rect x="300" y="20" rx="4" ry="4" width="80" height="12" />
          </ContentLoader>
        ))}
      </View>
    );
  }

  if (errorMessage) {
    return (
      <View style={styles.container}>
        <CustomText style={styles.errorMessage}>{errorMessage}</CustomText>
      </View>
    );
  }

  return (
    <AnimatedFlatList
      onLayout={onLayout}
      onScroll={onScroll}
      style={[styles.container, listStyle]}
      data={listData}
      refreshControl={
        <RefreshControl
          refreshing={isLoading}
          onRefresh={() => handleRefreshList()}
          tintColor={color.BLACK}
        />
      }
      keyExtractor={(item, index) => item.CAS_Guid}
      renderItem={({ item }) => {
        return (
          <CaseItem
            title={item.CAS_Title}
            casReference={item.CAS_Reference}
            assignedTo={item.USR_Name_AssignedTo}
            date={item.CAS_CreatedTimestamp}
            onPress={() => handleTapCase(item.CAS_Guid)}
          />
        );
      }}
      ListEmptyComponent={
        <View
          style={{
            justifyContent: "center",
            alignItems: "center",
            paddingTop: 70,
          }}
        >
          <CustomText>List Empty</CustomText>
        </View>
      }
      onEndReached={loadMore}
      ListFooterComponent={listFooterComponent}
    />
  );
};

export default CasesScreen;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
    },
    errorMessage: {
      color: color.ERROR,
      alignSelf: "center",
      paddingTop: SPACING.L,
    },
  });

  return { styles, color };
};
