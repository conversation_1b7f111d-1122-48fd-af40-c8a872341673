import React from "react";
import { StyleSheet, View } from "react-native";
import { RelatedUsersList } from "../../types/userMails";
import { CaseDetailsData } from "../../types/CaseDetailsData";
import { Message } from "../../types/message";
import { Task } from "../../types/task";

// Components
import TabBarNavigator from "./tabs/components/TabBarNavigator";
import CaseInfoHeader from "./tabs/components/caseInfoHeader";

// Styles
import { SPACING, Theme, useThemeAwareObject } from "b-ui-lib";

type Props = {
  caseData: CaseDetailsData;
  caseDateMessages: Message[];
  caseMessagesLoading: boolean;
  caseMessagesError: string;
  handleRefreshMessageList: () => {};
  comments?: any;
  caseAttachmentsIds: any;
  attachments: any;
  attachmentsCount: number;
  downloadedAttachments: number;
  recipientsEmails?: RelatedUsersList;
  caseDetailsLoading: boolean;
  caseDetailsError: string;
  getCaseCommentsLoading?: boolean;
  getCaseCommentsError?: string;
  clearDownloadedAttachments: () => void;
  handleAddAttachment?: () => void;
  uploadAttachmentLoading?: boolean;
  uploadAttachmentError?: string;
  postNewComment?: () => void;
  postMessageCommentLoading?: boolean;
  postMessageCommentError?: string;
  postMessageCommentSuccess?: boolean;
  attachmentsLength?: number;
  handlePostMessageCommentSuccessDismiss?: () => void;
  handleStarComment?: () => void;
  handleDownloadAttachment: () => void;
  handleDownloadAllAttachments: () => void;
  isAnyFileDownloadLoading: boolean;
  getMessageMetadataError: string;
  getMetadataLoading: boolean;
  caseMetadata: any;
  caseDate: string;
  caseLinkedCasesLoading: boolean;
  caseLinkedCasesError: string;
  handleRefreshLinkedCaseList: () => void;
  caseLinkedCases: CaseDetailsData[];
  handleTapCase?: (caseId: string) => void;
  tasks: Task[];
  caseTasksLoading: boolean;
  caseTasksError: string;
  handleRefreshTaskList: () => void;
  handleTapTask: (taskId: string) => void;
  notifiedUsers?: RelatedUsersList;
  fetchNotifiedUsers: () => void;
  fetchNotifiedUsersLoading: boolean;
  fetchNotifiedUsersError: string;
  onPressNewComment: () => void;
  onPressCommentReply: (commentId: string) => void;
};

const CaseScreen = ({
  caseData,
  caseDateMessages,
  caseMessagesLoading,
  caseMessagesError,
  handleRefreshMessageList,
  comments,
  caseDetailsLoading,
  caseDetailsError,
  caseAttachmentsIds,
  attachments,
  attachmentsCount,
  downloadedAttachments,
  recipientsEmails,
  clearDownloadedAttachments,
  postNewComment,
  postMessageCommentLoading,
  postMessageCommentError,
  postMessageCommentSuccess,
  handlePostMessageCommentSuccessDismiss,
  handleStarComment,
  handleAddAttachment,
  uploadAttachmentLoading,
  uploadAttachmentError,
  handleDownloadAttachment,
  handleDownloadAllAttachments,
  isAnyFileDownloadLoading,
  caseMetadata,
  getMessageMetadataError,
  getMetadataLoading,
  caseDate,
  attachmentsLength,
  caseLinkedCasesLoading,
  caseLinkedCasesError,
  handleRefreshLinkedCaseList,
  caseLinkedCases,
  handleTapCase,
  tasks,
  caseTasksLoading,
  caseTasksError,
  handleRefreshTaskList,
  handleTapTask,
  notifiedUsers,
  fetchNotifiedUsers,
  fetchNotifiedUsersLoading,
  fetchNotifiedUsersError,
  onPressNewComment,
  onPressCommentReply,
}: Props) => {
  const { styles } = useThemeAwareObject(createStyles);

  return (
    <View style={styles.container}>
      <CaseInfoHeader
        refNumber={caseData?.ref}
        subject={caseData?.title}
        sentDate={caseDate}
        assignedTo={
          recipientsEmails.find((item) => caseData?.assignTo === item.id)
            ?.value || "N/A"
        }
        containerStyle={styles.messageInfoContainer}
      />

      <TabBarNavigator
        caseData={caseData}
        caseDateMessages={caseDateMessages}
        caseMessagesLoading={caseMessagesLoading}
        caseMessagesError={caseMessagesError}
        handleRefreshMessageList={handleRefreshMessageList}
        relevantUsers={recipientsEmails}
        notifiedUsers={notifiedUsers}
        caseDetailsLoading={caseDetailsLoading}
        caseDetailsError={caseDetailsError}
        getMessageMetadataError={getMessageMetadataError}
        getMetadataLoading={getMetadataLoading}
        caseMetadata={caseMetadata}
        caseAttachmentsIds={caseAttachmentsIds}
        attachments={attachments}
        attachmentsCount={attachmentsCount}
        downloadedAttachments={downloadedAttachments}
        comments={comments}
        postNewComment={postNewComment}
        postMessageCommentLoading={postMessageCommentLoading}
        postMessageCommentError={postMessageCommentError}
        postMessageCommentSuccess={postMessageCommentSuccess}
        handlePostMessageCommentSuccessDismiss={
          handlePostMessageCommentSuccessDismiss
        }
        attachmentsLength={attachmentsLength}
        handleStarComment={handleStarComment}
        handleAddAttachment={handleAddAttachment}
        uploadAttachmentLoading={uploadAttachmentLoading}
        uploadAttachmentError={uploadAttachmentError}
        clearDownloadedAttachments={clearDownloadedAttachments}
        handleDownloadAttachment={handleDownloadAttachment}
        handleDownloadAllAttachments={handleDownloadAllAttachments}
        isAnyFileDownloadLoading={isAnyFileDownloadLoading}
        caseLinkedCasesLoading={caseLinkedCasesLoading}
        caseLinkedCasesError={caseLinkedCasesError}
        handleRefreshLinkedCaseList={handleRefreshLinkedCaseList}
        caseLinkedCases={caseLinkedCases}
        handleTapCase={handleTapCase}
        tasks={tasks}
        caseTasksLoading={caseTasksLoading}
        caseTasksError={caseTasksError}
        handleRefreshTaskList={handleRefreshTaskList}
        handleTapTask={handleTapTask}
        notifiedUsers={notifiedUsers}
        fetchNotifiedUsers={fetchNotifiedUsers}
        fetchNotifiedUsersLoading={fetchNotifiedUsersLoading}
        fetchNotifiedUsersError={fetchNotifiedUsersError}
        onPressNewComment={onPressNewComment}
        onPressCommentReply={onPressCommentReply}
      />
    </View>
  );
};

export default CaseScreen;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
      flex: 1,
    },
    messageInfoContainer: {
      padding: SPACING.M,
    },
  });

  return { styles, color };
};
