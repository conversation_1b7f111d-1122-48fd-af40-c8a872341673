import React from "react";
import { Safe<PERSON>reaView, StyleSheet, View } from "react-native";

// Components
import ReactNativeModal from "react-native-modal";
import {
  type Theme,
  type Folder,
  useThemeAwareObject,
  FolderList,
  CustomText,
  SPACING,
  IconButton,
} from "b-ui-lib";
import { TEST_IDS } from "../../constants/testIds";

type Props = {
  isBurgerMenuOpen: boolean;
  openBurgerMenu: () => void;
  closeBurgerMenu: () => void;
  folders: Folder[];
  handleToggleFolderExpand: (folderId: string) => void;
  onSelectFolder: (folderId: string) => void;
  selectedFolderId: string;
  handleFolderPressSearchItem: (folderId: string) => void;
  onAnimationComplete?: () => void;
};

const BurgerMenuScreen: React.FC = ({
  isBurgerMenuOpen,
  openBurgerMenu,
  closeBurgerMenu,
  folders,
  handleToggleFolderExpand,
  onSelectFolder,
  selectedFolderId,
  handleFolderPressSearchItem,
  onAnimationComplete,
}: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  return (
    <>
      <IconButton
        testID={TEST_IDS.inboxHeaderBurgerIcon}
        name="menu"
        size={16}
        color={color.TEXT_DIMMED}
        onPress={openBurgerMenu}
        containerStyle={styles.menuIcon}
      />

      <ReactNativeModal
        style={styles.drawer}
        isVisible={isBurgerMenuOpen}
        hasBackdrop={true}
        backdropOpacity={0.01}
        onBackButtonPress={closeBurgerMenu}
        onBackdropPress={closeBurgerMenu}
        animationIn={"slideInLeft"}
        animationOut={"slideOutLeft"}
        onModalShow={onAnimationComplete}
        coverScreen
      >
        <SafeAreaView style={styles.drawerContent}>
          <View style={styles.drawerHeader}>
            <IconButton
              name="menu"
              size={16}
              color={color.TEXT_DIMMED}
              onPress={closeBurgerMenu}
              containerStyle={styles.menuIconDrawerMenu}
            />

            <CustomText style={styles.categoriesText}>Categories</CustomText>

            <View style={{ width: 44 }} />
          </View>

          <FolderList
            folders={folders}
            handleFolderExpand={handleToggleFolderExpand}
            handleFolderPress={onSelectFolder}
            selectedFolderId={selectedFolderId}
            handleFolderPressSearchItem={handleFolderPressSearchItem}
          />
        </SafeAreaView>
      </ReactNativeModal>
    </>
  );
};

export default BurgerMenuScreen;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    menuIcon: {
      padding: SPACING.S,
    },
    drawer: {
      margin: 0,
      backgroundColor: color.BACKGROUND,
    },
    drawerContent: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
      justifyContent: "space-between",
    },
    drawerHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      padding: SPACING.S,
      borderBottomColor: color.BACKGROUND,
      borderBottomWidth: 1,
      alignItems: "center",
      backgroundColor: color.BACKGROUND,
    },
    menuIconDrawerMenu: {
      padding: SPACING.S,
      backgroundColor: color.TAB_BACKGROUND,
      borderRadius: 6,
    },
    categoriesText: {
      fontWeight: "700",
      fontSize: 14,
    },
  });

  return { styles, color };
};
