import React from "react";
import { StyleSheet } from "react-native";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { SCREEN_NAMES } from "../constants/screenNames";
import { useSelector } from "react-redux";
import { SafeAreaView } from "react-native-safe-area-context";

// Components
import { NavigationContainer } from "@react-navigation/native";
import LoginHOC from "../containers/LoginHOC";
import { type Theme, useThemeAwareObject } from "b-ui-lib";
import UrlParamsHOC from "../containers/UrlParamsHOC";

const Stack = createNativeStackNavigator();

const RootNavigation: React.FC = () => {
  const { token } = useSelector((state) => state.persist.bTeamAuth);
  const { styles } = useThemeAwareObject(createStyles);

  return (
    <SafeAreaView style={styles.container}>
      <NavigationContainer>
        <Stack.Navigator
          initialRouteName={SCREEN_NAMES.login}
          screenOptions={{ headerShown: false }}
        >
          {!!token ? (
            <>
              <Stack.Screen
                name={SCREEN_NAMES.tabsNavigation}
                component={UrlParamsHOC}
              />
            </>
          ) : (
            <Stack.Screen name={SCREEN_NAMES.login} component={LoginHOC} />
          )}
        </Stack.Navigator>
      </NavigationContainer>
    </SafeAreaView>
  );
};

const createStyles = ({ color, images }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
    },
  });

  return { styles, color, images };
};

export default RootNavigation;
