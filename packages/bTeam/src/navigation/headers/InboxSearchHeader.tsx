import React, { RefObject } from "react";
import {
  Keyboard,
  StyleSheet,
  TextInput,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import { NavigationProp, ParamListBase } from "@react-navigation/native";
import {
  type Theme,
  SearchInput,
  SPACING,
  useThemeAwareObject,
  AdvancedSearchButtons,
  CustomText,
} from "b-ui-lib";
import { SCREEN_NAMES } from "../../constants/screenNames";
import { TEST_IDS } from "../../constants/testIds";
import { InboxSearchFields } from "../../types/inboxSearchFields";
import { Folder } from "../../types/folder";

type Props = {
  navigation: NavigationProp<ParamListBase>;
  inboxSearchFields: InboxSearchFields;
  appliedSearchFiltersCount?: number;
  searchInputRef: RefObject<TextInput>;
  isQuickSearchEnabled?: boolean;
  validationErrorMessage?: string;
  handleClearAllButton: () => void;
  handleSearchInputClear: () => void;
  handleSearchInputChange: (inputValue: string) => void;
  handleSearchInputFetchGridMessages: (inputValue: string) => void;
  filteredSuggestions: string[];
  handleSuggestionPress: (suggestion: string) => void;
  selectedFolderId: string;
  foldersById: { [key: string]: Folder };
};

const InboxSearchHeader = ({
  navigation,
  inboxSearchFields,
  appliedSearchFiltersCount,
  searchInputRef,
  isQuickSearchEnabled,
  validationErrorMessage,
  handleClearAllButton,
  handleSearchInputClear,
  handleSearchInputChange,
  handleSearchInputFetchGridMessages,
  filteredSuggestions,
  handleSuggestionPress,
  selectedFolderId,
  foldersById,
}: Props) => {
  const { styles } = useThemeAwareObject(createStyles);

  const navigateToInboxSearchFilters = () =>
    navigation.navigate(SCREEN_NAMES.inboxSearchFilters);

  const onOutsidePress = () => {
    if (Keyboard.isVisible()) {
      Keyboard.dismiss();
    }
  };

  return (
    <TouchableWithoutFeedback onPress={onOutsidePress}>
      <View style={styles.container}>
        <SearchInput
          testID={TEST_IDS.inboxSearchInput}
          searchInputRef={searchInputRef}
          value={inboxSearchFields?.searchText}
          containerStyle={styles.searchInput}
          onChangeText={handleSearchInputChange}
          handleDebounceFunction={handleSearchInputFetchGridMessages}
          handleInputClear={handleSearchInputClear}
          placeholder={"Search here"}
          suggestions={filteredSuggestions}
          handleSuggestionPress={handleSuggestionPress}
          errorText={validationErrorMessage}
          // We pass debounceParameters to ensure the debounce function receives the latest values.
          // Otherwise, because of internally useCallback, it would only capture the initial values.
          debounceParameters={{
            currentFolderId: selectedFolderId,
            foldersById,
          }}
        />

        {validationErrorMessage ? (
          <CustomText style={styles.errorMessage}>
            {validationErrorMessage}
          </CustomText>
        ) : null}

        <AdvancedSearchButtons
          searchFiltersCount={
            !isQuickSearchEnabled && appliedSearchFiltersCount > 0
              ? appliedSearchFiltersCount
              : 0
          }
          isClearAllButtonVisible={
            !isQuickSearchEnabled && appliedSearchFiltersCount > 0
          }
          handleClearAll={handleClearAllButton}
          handlePressCriteriaButton={navigateToInboxSearchFilters}
          style={{ buttonsContainer: styles.buttonsHeader }}
        />
      </View>
    </TouchableWithoutFeedback>
  );
};

export default InboxSearchHeader;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: color.BACKGROUND,
    },
    searchInput: {
      marginVertical: SPACING.XXS,
      marginHorizontal: SPACING.M,
      zIndex: 1000,
    },
    buttonsHeader: {
      paddingVertical: SPACING.XXS,
      paddingHorizontal: SPACING.M,
    },
    errorMessage: {
      color: color.ERROR,
      paddingHorizontal: SPACING.M,
      fontSize: 12,
    },
  });

  return { styles, color };
};
