import env from "react-native-config";
import { checkPermission, FILE_DOWNLOAD_STATUS } from "bcomponents";
import Toast from "react-native-toast-message";

const showDownloadStatus = (
  filename: string,
  downloadStatus: FileDownloadStatus
) => {
  // Display toast notifications based on status
  if (downloadStatus === FILE_DOWNLOAD_STATUS.loading) {
    Toast.show({
      type: "info",
      text1: "Download Started",
      text2: filename
        ? `Downloading file ${filename}...`
        : `Downloading file...`,
      position: "top",
    });
  } else if (downloadStatus === FILE_DOWNLOAD_STATUS.completed) {
    Toast.show({
      type: "success",
      text1: "Download Complete",
      text2: filename
        ? `File ${filename} downloaded successfully.`
        : `File downloaded successfully.`,
      position: "top",
    });
  } else if (downloadStatus === FILE_DOWNLOAD_STATUS.failed) {
    Toast.show({
      type: "error",
      text1: "Download Failed",
      text2: filename
        ? `File ${filename} could not be downloaded.`
        : `File could not be downloaded.`,
      position: "top",
    });
  }
};

type FileDownloadStatus =
  (typeof FILE_DOWNLOAD_STATUS)[keyof typeof FILE_DOWNLOAD_STATUS];

/**
 * Helper function to download a single attachment.
 * @param {string} token - Authentication token.
 * @param {string} attachmentId - The ID of the file to download.
 * @param {string} fileName - Name of the file.
 * @param {(attachmentId: string, status: FileDownloadStatus) => void} updateDownloadStatus - Callback to update download state.
 * @param {string} url - File url.
 * @param {boolean} previewFile - Preview file after download.
 */
export const downloadSingleAttachment = async (
  token: string,
  attachmentId: string,
  fileName: string,
  updateDownloadStatus: (
    attachmentId: string,
    status: FileDownloadStatus
  ) => void,
  url?: string,
  previewFile?: boolean
) => {
  try {
    const fileUrl = url
      ? url
      : `${env.API_URL_BTEAM_MESSAGES}/FileLinkFileDownload.aspx?FLN_Guid=${attachmentId}`;

    await checkPermission({
      fileUrl,
      fileName,
      token: { access_token: token },
      filesDownloadStatus: (downloadStatus: FileDownloadStatus) => {
        showDownloadStatus(fileName, downloadStatus);
        updateDownloadStatus && updateDownloadStatus(attachmentId, downloadStatus);
      },
      showAlerts: false,
      previewFile,
    });
  } catch (error) {
    console.error(`Error downloading file ${fileName}:`, error);
    updateDownloadStatus && updateDownloadStatus(attachmentId, FILE_DOWNLOAD_STATUS.failed);
  }
};

/**
 * Helper function to download multiple attachments.
 * @param {string} token - Authentication token.
 * @param {string[]} attachmentIds - List of attachment IDs to download.
 * @param {Record<string, string>} fileNames - Object mapping attachment IDs to file names.
 * @param {(attachmentId: string, status: FileDownloadStatus) => void} updateDownloadStatus - Callback to update download state.
 */
export const downloadMultipleAttachments = async (
  token: string,
  attachmentIds: string[],
  fileNames: Record<string, string>,
  updateDownloadStatus: (
    attachmentId: string,
    status: FileDownloadStatus
  ) => void
) => {
  try {
    for (const attachmentId of attachmentIds) {
      await downloadSingleAttachment(
        token,
        attachmentId,
        fileNames[attachmentId] || "unknown",
        updateDownloadStatus
      );
    }
  } catch (error) {
    console.error("Error downloading multiple attachments:", error);
  }
};
