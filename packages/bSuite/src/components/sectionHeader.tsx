import React from "react";
import { StyleSheet, View } from "react-native";
import { Tag, TitleGroupedCards } from "bcomponents";

// Styles
import { SPACING } from "bstyles";
import { Theme } from "btheme/ThemeInterface";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";
import BenefitIconSet from "engine/src/assets/fonts/icons";

type Props = {};

const SectionHeader = ({ title }) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  return (
    <>
      {title.name ? (
        <View style={styles.container}>
          <BenefitIconSet
            name="cargo-ship"
            size={20}
            color={color.HALF_DIMMED}
            marginRight={SPACING.XS}
          />

          <TitleGroupedCards
            title={title?.name}
            textStyles={{ fontSize: 16 }}
          />

          <Tag
            count={title?.sumOfApprovals || "0"}
            backgroundColor={color.PRESSABLE}
            fontColor={color.BRAND_DEFAULT}
            fontWeight={"700"}
          />
        </View>
      ) : (
        <View style={{ padding: 8 }}></View>
      )}
    </>
  );
};

export default SectionHeader;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: color.BACKGROUND,
      paddingHorizontal: SPACING.M,
      paddingTop: SPACING.S,
      paddingBottom: SPACING.M,
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.1,
      shadowRadius: 1.41,
    },
  });

  return { styles, color };
};
