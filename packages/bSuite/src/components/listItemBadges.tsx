import React from "react";
import { View } from "react-native";
import { CustomText } from "bcomponents";

// Styles
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";

type Props = {};

const ListItemBadges = ({}: Props) => {
  const { color } = useThemeAwareObject((color) => color);

  return (
    <View
      style={{
        flexDirection: "row",
        alignItems: "center",
        paddingHorizontal: 8,
        backgroundColor: color.BRAND_DEFAULT,
        borderRadius: 20,
      }}
    >
      <View
        style={{
          width: 8,
          height: 8,
          backgroundColor: color.TEXT_DEFAULT,
          borderRadius: 50,
          marginRight: 5,
        }}
      />

      <CustomText>!</CustomText>
    </View>
  );
};

export default ListItemBadges;
