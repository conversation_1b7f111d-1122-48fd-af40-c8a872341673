import React, { useState, useEffect } from "react";
import { Platform, Pressable, StyleSheet, Text, View } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { useSelector, useDispatch } from "react-redux";
import { routeNames } from "../navigation/routeNames";
import { setScrollIndex } from "../slices/approvalsSlice";

// Components
import { CustomText, Tag } from "bcomponents";

// Styles
import { SPACING } from "bstyles";
import { Theme } from "btheme/ThemeInterface";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";
import BenefitIconSet from "engine/src/assets/fonts/icons";
import ListItemBadges from "./listItemBadges";

type Props = {
  item: any;
  index: number;
};

const DetailedListItem = ({ item, index }: Props) => {
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const { styles, color } = useThemeAwareObject(createStyles);
  const { categories, taskStatusMapper, scrollIndex } = useSelector(
    (state) => state.root.approvalsReducer
  );
  const [shouldExpand, setShouldExpand] = useState(false);
  const [isHeighlighted, setIsHeighlighted] = useState(false);
  const itemCategory = categories[item.categoryId];

  // Function to truncate text with ellipsis
  const truncateText = (text, maxLength) => {
    return text && text.length > maxLength
      ? text.substring(0, maxLength) + "..."
      : text;
  };

  // Effect to highlight the item if it's the current scroll index
  useEffect(() => {
    if (index === scrollIndex) {
      setIsHeighlighted(true);
      setShouldExpand(true);

      setTimeout(() => {
        setIsHeighlighted(false);
      }, 2000);
    }
  }, [index, scrollIndex]);

  const onPress = () => {
    setShouldExpand(false);

    dispatch(setScrollIndex(index));

    navigation.navigate({
      name:
        itemCategory.id === 40
          ? routeNames.EvaluationTaskHOC
          : routeNames.ApprovalTaskHOC,
      params: {
        taskNumber: item.taskNumber,
        documentType: itemCategory.id,
        taskId: item.documentId,
      },
    });
  };

  // Safely handle null values
  const supplierText = item?.supplier ?? ""; // Default to empty string if null
  const expensesText = item?.expenses ?? ""; // Default to empty string if null

  return (
    <Pressable
      style={[
        styles.container,
        isHeighlighted ? { backgroundColor: color.HIGHLIGHT } : {},
      ]}
      onPress={onPress}
    >
      <View
        style={{
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: SPACING.XS,
        }}
      >
        <CustomText>{item?.vesselName}</CustomText>

        <View style={{ flexDirection: "row", alignItems: "center", gap: 10 }}>
          {item?.isImportant === 1 && <ListItemBadges />}

          <BenefitIconSet
            name={taskStatusMapper[item?.status]?.iconName}
            size={20}
            color={taskStatusMapper[item?.status]?.color}
          />
        </View>
      </View>

      <View
        style={{
          flexDirection: "row",
          justifyContent: "space-between",
        }}
      >
        <CustomText style={styles.labelText}>
          {itemCategory?.categoryName}
        </CustomText>

        <CustomText style={styles.valueText}>{item?.docRef}</CustomText>
      </View>

      <View
        style={[
          styles.expandableText,
          { flexWrap: shouldExpand ? "wrap" : null },
        ]}
      >
        <View
          style={{
            flex: 1,
            flexDirection: "row",
            justifyContent: "space-between",
          }}
        >
          <CustomText style={styles.labelText}>Supplier</CustomText>

          <CustomText
            style={styles.valueText}
            numberOfLines={shouldExpand ? 0 : 1}
          >
            {shouldExpand ? supplierText : truncateText(supplierText, 18)}
          </CustomText>
        </View>
      </View>

      <View
        style={[
          styles.expandableText,
          { flexWrap: shouldExpand ? "wrap" : null },
        ]}
      >
        <View
          style={[
            {
              flex: 1,
              flexDirection: "row",
              justifyContent: "space-between",
            },
            { flexWrap: shouldExpand ? "wrap" : null },
          ]}
        >
          <CustomText style={styles.labelText}>Expenses</CustomText>

          <CustomText
            style={styles.valueText}
            numberOfLines={shouldExpand ? 0 : 1}
          >
            {shouldExpand ? expensesText : truncateText(expensesText, 18)}
          </CustomText>
        </View>
      </View>

      <View style={styles.taskStatusWrapper}>
        <Pressable onPress={() => setShouldExpand(!shouldExpand)}>
          {(supplierText.length > 18 || expensesText.length > 18) && (
            <CustomText style={{ color: color.TEXT_DIMMED }}>
              {!shouldExpand ? (
                <BenefitIconSet
                  name="chevron-down"
                  size={25}
                  color={color.HALF_DIMMED}
                />
              ) : (
                <BenefitIconSet
                  name="chevron-up"
                  size={25}
                  color={color.HALF_DIMMED}
                />
              )}
            </CustomText>
          )}
        </Pressable>

        <View
          style={[
            styles.expandableText,
            { flexWrap: shouldExpand ? "wrap" : null },
          ]}
        >
          <CustomText
            style={{
              fontSize: 14,
              fontWeight: "700",
              color: color.BRAND_DEFAULT,
            }}
            numberOfLines={shouldExpand ? 0 : 1}
          >
            {item?.amount}
          </CustomText>
        </View>
      </View>
    </Pressable>
  );
};

export default DetailedListItem;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: color.PRESSABLE,
      marginBottom: SPACING.XS,
      borderRadius: SPACING.XS,
      padding: SPACING.S,
      marginHorizontal: SPACING.M,
      minHeight: 100,
    },
    labelText: {
      fontSize: 12,
      flex: 1,
    },
    valueText: {
      fontSize: 12,
      flex: 1,
      textAlign: "right",
    },
    taskStatusWrapper: {
      flex: 1,
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    iconWrapper: {
      backgroundColor: color.PRESSABLE_HOVER,
      width: 30,
      height: 30,
      borderRadius: 50,
      justifyContent: "center",
      alignItems: "center",
      marginBottom: SPACING.XS,
    },
    expandableText: {
      // flexDirection: "row",
      // alignItems: "center",
    },
  });

  return { styles, color };
};
