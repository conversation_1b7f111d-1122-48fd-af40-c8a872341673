import React, { useState } from "react";
import { Pressable, StyleSheet, View } from "react-native";

// Components
import { TitleGroupedCards, CustomText } from "bcomponents";

// Styles
import { SPACING } from "bstyles";
import { Theme } from "btheme/ThemeInterface";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";
import BenefitIconSet from "engine/src/assets/fonts/icons";

type Props = {
  taskComments: string | undefined;
};

const TaskComments = ({ taskComments }: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);
  const [expandedDescription, setExpandedDescription] = useState(false);
  const [isLongText, setIsLongText] = useState(false);
  const MAX_LINES = 3;

  const onTextLayout = ({ nativeEvent: { lines } }) => {
    setIsLongText(lines.length > MAX_LINES);
  };

  return (
    <>
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          marginBottom: SPACING.M,
        }}
      >
        <BenefitIconSet
          name="Align-Left"
          size={16}
          color={color.TEXT_DEFAULT}
          marginRight={SPACING.XXS}
        />
        <TitleGroupedCards title="Comments" />
      </View>

      <View style={styles.section}>
        <CustomText
          onTextLayout={onTextLayout}
          style={{ color: color.TEXT_DEFAULT, fontSize: 12 }}
          numberOfLines={expandedDescription ? 0 : 4}
        >
          {taskComments?.trim()}
        </CustomText>

        <Pressable onPress={() => setExpandedDescription(!expandedDescription)}>
          {!!taskComments && isLongText && (
            <CustomText style={{ color: color.TEXT_DIMMED }}>
              {!expandedDescription ? "...show more" : "show less"}
            </CustomText>
          )}
        </Pressable>
      </View>
    </>
  );
};

export default TaskComments;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    section: {
      backgroundColor: color.PRESSABLE,
      padding: SPACING.S,
      borderRadius: SPACING.SIX,
      marginBottom: SPACING.M,
    },
  });

  return { styles, color };
};
