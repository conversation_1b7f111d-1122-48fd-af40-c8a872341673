import React from "react";
import { ViewStyle } from "react-native";

// Styles
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";
import BenefitIconSet from "engine/src/assets/fonts/icons";

type Props = {
  taskStatus: 0 | 1 | 2 | 3 | 4 | 5 | 6 | undefined; // Status του Task (0: Pending, 1: Accepted/Replied, 2: Rejected, 3: Cancelled, 4: Waiting – Ask for Clarifications, 5: Returned, 6: Redirected)
  style?: ViewStyle;
};

const TaskStatusIcon = ({ taskStatus, style }: Props) => {
  const { color } = useThemeAwareObject((color) => color);

  // Todo: take this data from redux
  const statusMapper = {
    0: { name: "Quick-View", color: color.TEXT_DEFAULT },
    1: { name: "check-circle", color: color.SUCCESS },
    2: { name: "x-circle", color: color.DESTRUCTIVE_DEFAULT },
    3: { name: "disabled", color: color.DESTRUCTIVE_DEFAULT },
    4: { name: "clock", color: color.TEXT_DEFAULT },
    5: { name: "corner-down-left", color: color.TEXT_DEFAULT },
    6: { name: "corner-down-left", color: color.TEXT_DIMMED },
    7: { name: "check-circle", color: color.TEXT_DIMMED },
    8: { name: "review", color: color.TEXT_DIMMED },
    9: { name: "help", color: color.TEXT_DIMMED },
    allIds: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
  };

  return (
    <BenefitIconSet
      name={statusMapper[taskStatus]?.name}
      size={24}
      color={statusMapper[taskStatus]?.color}
      {...style}
    />
  );
};

export default TaskStatusIcon;
