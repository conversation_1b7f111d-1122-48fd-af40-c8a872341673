import React from "react";
import { StyleSheet, Pressable } from "react-native";

// Components
import { CustomText } from "bcomponents/";

// Styles
import { SPACING, FONTSIZES } from "bstyles";
import { Theme } from "btheme/ThemeInterface";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";
import BenefitIconSet from "engine/src/assets/fonts/icons";

type Props = {
  name: string;
  iconColor: string;
  onPress: () => void;
  selected?: boolean;
  actionName: string;
};

const ActionItem = ({
  name,
  iconColor,
  onPress,
  selected,
  actionName,
}: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  return (
    <Pressable
      style={[
        styles.actionItemWrapper,
        { backgroundColor: selected ? color.HIGHLIGHT : color.PRESSABLE },
      ]}
      onPress={onPress}
    >
      <BenefitIconSet
        name={name}
        size={24}
        color={iconColor}
        marginRight={SPACING.S}
      />
      <CustomText>{actionName}</CustomText>
    </Pressable>
  );
};

export default ActionItem;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    actionItemWrapper: {
      backgroundColor: color.PRESSABLE,
      marginBottom: SPACING.XS,
      borderRadius: SPACING.XS,
      padding: 14,
      flexDirection: "row",
      alignItems: "center",
    },
  });

  return { styles, color };
};
