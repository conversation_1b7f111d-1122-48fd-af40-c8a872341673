import React, { useState } from "react";
import { StyleSheet, Pressable, View, InteractionManager } from "react-native";
import { useSelector } from "react-redux";

// Components
import { CustomText } from "bcomponents/";
import ActionModal from "./actionModal";
import SelectActionModal from "./selectActionModal";

// Styles
import { SPACING } from "bstyles";
import { Theme } from "btheme/ThemeInterface";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";
import BenefitIconSet from "engine/src/assets/fonts/icons";

type Props = {
  vesselName: string;
  type?: string;
  documentNumber: number | null;
  taskRef: string;
  taskId: number | null;
  setLastActionPerformedItem: (id: any) => void;
};

const ApprovalTaskHeader = ({
  vesselName,
  type,
  documentNumber,
  taskRef,
  taskId,
  setLastActionPerformedItem,
}: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);
  const [selecteActionModal, setSelecteActionModal] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedAction, setSelectedAction] = useState<number | null>(null);
  const taskData = useSelector((state) => state.root.approvalsReducer.taskData);
  const handleSelectAction = (actionId: number) => {
    setSelectedAction(actionId);
    setSelecteActionModal(false);

    InteractionManager.runAfterInteractions(() => {
      setIsModalVisible(true);
    });
  };

  const handleModalOpen = () => {
    if (!selectedAction) {
      setSelecteActionModal(true);
    } else {
      setIsModalVisible(true);
    }
  };

  return (
    <View style={styles.header}>
      <View>
        <CustomText>{vesselName}</CustomText>

        <CustomText style={{}}>
          <CustomText style={{ fontWeight: "700", fontSize: 12 }}>
            {type}
          </CustomText>{" "}
          no{" "}
          <CustomText style={{ fontWeight: "700", fontSize: 12 }}>
            {documentNumber}
          </CustomText>
          , ref{" "}
          <CustomText style={{ fontWeight: "700", fontSize: 12 }}>
            {taskRef}
          </CustomText>
        </CustomText>
      </View>

      <Pressable
        style={{
          backgroundColor: color.BRAND_DEFAULT,
          padding: SPACING.XS,
          borderRadius: SPACING.SIX,
        }}
        onPress={handleModalOpen}
      >
        <CustomText style={{ fontWeight: "700", fontSize: 12 }}>
          Select Action
        </CustomText>
      </Pressable>

      <SelectActionModal
        isVisible={selecteActionModal}
        setIsVisible={setSelecteActionModal}
        onSelectAction={handleSelectAction}
        taskData={taskData}
      />

      <ActionModal
        isVisible={isModalVisible}
        setIsVisible={setIsModalVisible}
        taskId={taskId}
        selectedAction={selectedAction}
        setSelectedAction={setSelectedAction}
        canAddParticipant={taskData.canAddParticipant}
        taskData={taskData}
        setLastActionPerformedItem={setLastActionPerformedItem}
      />
    </View>
  );
};

export default ApprovalTaskHeader;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    header: {
      backgroundColor: color.PRESSABLE,
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      padding: SPACING.S,
      borderRadius: SPACING.SIX,
      marginBottom: SPACING.M,
    },
  });

  return { styles, color };
};
