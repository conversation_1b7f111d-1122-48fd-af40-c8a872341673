import React, {
  useState,
  SetStateAction,
  useRef,
  useEffect,
  Dispatch,
} from "react";
import {
  StyleSheet,
  Pressable,
  TextInput,
  View,
  KeyboardAvoidingView,
  Alert,
  Platform,
} from "react-native";
import { useQueryClient, useMutation } from "react-query";
import { useSelector } from "react-redux";
import { routeNames } from "../../navigation/routeNames";
import putApprovalAction from "../../queries/putApprovalAction";
import { TaskInfo } from "../../slices/approvalsSlice";
import { queryKeys } from "../../queries/queryKeys";
import { useNavigation } from "@react-navigation/native";
import { Picker } from "@react-native-picker/picker";
import { useAuthContext } from "../../contexts/AuthContext";

// Components
import {
  MyModal,
  CustomText,
  ModalCloseImage,
  Tag,
  IconButton,
} from "bcomponents";

// Styles
import { SPACING } from "bstyles";
import { Theme } from "btheme/ThemeInterface";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";
import BenefitIconSet from "engine/src/assets/fonts/icons";

type Props = {
  isVisible: boolean;
  setIsVisible: Dispatch<SetStateAction<boolean>>;
  taskId: number | null;
  selectedAction: number | null;
  setSelectedAction: Dispatch<SetStateAction<number | null>>;
  canAddParticipant: number;
  setLastActionPerformedItem: (id: any) => void;
};

const ActionModal = ({
  isVisible,
  setIsVisible,
  taskId,
  selectedAction,
  setSelectedAction,
  canAddParticipant,
  taskData,
  setLastActionPerformedItem,
}: Props) => {
  const queryClient = useQueryClient();
  const navigation = useNavigation();
  const { responsibles, taskStatusMapper } = useSelector(
    (state) => state.root.approvalsReducer
  );

  const { token, companyInfo } = useAuthContext();
  const baseUrl = companyInfo.bSuiteUrl;

  const { styles, color } = useThemeAwareObject(createStyles);
  const [comment, setComment] = useState("");
  const [pickerFocused, setPickerFocused] = useState(false);
  const [selectedPersons, setSelectedPersons] = useState<number[] | null>([]);
  const taskDataRef = useRef<TaskInfo | null>(null);

  const handleCancel = () => {
    setComment("");
    setSelectedPersons([]);
    setSelectedAction(null);
    setView("FormView");

    setIsVisible(false);
  };

  const handleValueChange = (value) => {
    if (value && selectedPersons.indexOf(value) === -1) {
      setSelectedPersons([...selectedPersons, value]);
    }
  };

  const viewDictionary = {
    FormView: (
      <View style={{ paddingBottom: Platform.OS === "ios" ? SPACING.THIRTY : 0 }}>
        <View style={{ flexDirection: "row" }}>
          <CustomText style={[styles.title, { marginRight: 5 }]}>
            Selected Action:{" "}
            <CustomText
              style={[styles.title, { fontSize: 17, fontWeight: "700" }]}
            >
              {taskStatusMapper[selectedAction]?.name}
            </CustomText>
          </CustomText>

          <BenefitIconSet
            name={taskStatusMapper[selectedAction]?.iconName}
            size={24}
            color={taskStatusMapper[selectedAction]?.color}
          />
        </View>

        {selectedAction !== 4 && canAddParticipant === 1 && (
          <>
            {selectedAction !== 2 && (
              <CustomText style={styles.title}>
                {selectedAction === 5
                  ? "Select next participants"
                  : "Select redirect participants"}
              </CustomText>
            )}

            {selectedAction !== 2 && (
              <View style={styles.inputWrapper}>
                <Picker
                  mode="dialog"
                  onFocus={() => setPickerFocused(true)}
                  onBlur={() => setPickerFocused(false)}
                  onValueChange={handleValueChange}
                  dropdownIconColor={color.HALF_DIMMED}
                >
                  <Picker.Item
                    label="Select User"
                    value=""
                    color={color.TEXT_DIMMED}
                    style={{ backgroundColor: color.BACKGROUND }}
                  />
                  {responsibles.allIds.map((id) => (
                    <Picker.Item
                      key={id}
                      value={id}
                      label={responsibles[id].responsible}
                      color={color.TEXT_DEFAULT}
                      style={{ backgroundColor: color.BACKGROUND }}
                    />
                  ))}
                </Picker>
              </View>
            )}
          </>
        )}

        <View
          style={{
            flexDirection: "row",
            flexWrap: "wrap",
            marginTop: SPACING.S,
          }}
        >
          {selectedPersons?.map((id) => (
            <Pressable
              key={id}
              style={styles.button}
              onPress={() =>
                setSelectedPersons(
                  selectedPersons.filter((item) => item !== id)
                )
              }
            >
              <Tag
                title={responsibles[id]?.responsible}
                fontColor={color.TEXT_DEFAULT}
              />

              <BenefitIconSet
                name="x"
                size={16}
                color={color.DESTRUCTIVE_DEFAULT}
              />
            </Pressable>
          ))}
        </View>

        <View style={styles.inputWrapper}>
          <TextInput
            placeholder="Comments"
            value={comment}
            onChangeText={(text) => setComment(text)}
            placeholderTextColor={color.TEXT_DIMMED}
            style={{
              color: color.TEXT_DEFAULT,
              paddingVertical: Platform.OS === "ios" ? SPACING.THIRTY : 5,
              marginTop: Platform.OS === "ios" ? SPACING.THIRTY : 5,
            }}
            multiline
            numberOfLines={3}
          />
        </View>

        <View style={styles.buttonWrapper}>
          <IconButton
            buttonText="Cancel"
            style={styles.button}
            onPress={() => onCancel()}
          />

          <IconButton
            buttonText="Submit"
            style={styles.button}
            iconName="arrow-right"
            iconSize={24}
            iconColor={color.TEXT_DEFAULT}
            onPress={() => handleSubmit()}
          />
        </View>
      </View>
    ),
    CancelView: (
      <>
        <View style={{ flexDirection: "row" }}>
          <CustomText style={[styles.title, { marginRight: 5 }]}>
            Clear participants and comments and cancel the action?
          </CustomText>
        </View>
        <View style={styles.buttonWrapper}>
          <IconButton
            buttonText="Back"
            style={styles.button}
            onPress={() => setView("FormView")}
          />

          <IconButton
            buttonText="Clear data"
            style={styles.button}
            onPress={() => handleCancel()}
          />
        </View>
      </>
    ),
  };

  const [view, setView] = useState("FormView");

  useEffect(() => {
    taskDataRef.current = taskData;
  }, []);

  const mutation = useMutation(
    () =>
      putApprovalAction(
        baseUrl,
        token,
        taskId,
        selectedAction,
        comment,
        selectedPersons
      ),
    {
      onError: (error) => {
        console.log("errror", error.response.data);

        if (error?.response.data.trim().includes("not exists")) {
          Alert.alert(
            error?.response?.data?.Message,
            "The task you are trying to update does not exists anymore."
          );

          // queryClient.invalidateQueries(queryKeys.categoryApprovals());
          setIsVisible(false);

          navigation.goBack();
        }

        if (
          error?.response?.data?.Message.trim().includes("not exists") ||
          error?.response.data.trim().includes("not exists")
        ) {
          Alert.alert(
            error?.response?.data?.Message,
            "The task you are trying to update does not exists anymore."
          );

          queryClient.invalidateQueries(queryKeys.taskInfo);
          setIsVisible(false);
        }

        if (
          error?.response?.data?.Message.trim().includes(
            "Please select receiver(s)!"
          )
        ) {
          Alert.alert(error?.response?.data?.Message);
        }
      },
      onSuccess: (data) => {
        queryClient.invalidateQueries(queryKeys.taskInfo);

        setLastActionPerformedItem({ id: taskId, status: selectedAction });
        setIsVisible(false);
        navigation.navigate(routeNames.singleCategoryList);
      },
    }
  );

  const onCancel = () => {
    setView("CancelView");
  };

  const handleSubmit = async () => {
    await queryClient.invalidateQueries(queryKeys.taskInfo).then((e) => {
      let isChanged = false;

      if (taskDataRef.current && taskDataRef.current.taskId === taskId) {
        for (const key of Object.keys(taskData)) {
          if (taskData[key] !== taskDataRef.current[key]) {
            console.log("here");

            isChanged = true;
            break; // Stop the loop once a change is found
          }
        }
      }

      if (isChanged) {
        // Optionally, show a message to the user
        Alert.alert(
          "Document data has changed",
          "Please review the new data, before taking any action."
        );

        setIsVisible(false);
      } else {
        mutation.mutate();
      }
    });
  };

  return (
    <MyModal open={isVisible} close={() => setIsVisible(false)}>
      <KeyboardAvoidingView behavior="padding" style={styles.modalContainer}>
        <ModalCloseImage />

        {viewDictionary[view]}
      </KeyboardAvoidingView>
    </MyModal>
  );
};
export default ActionModal;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    modalContainer: {
      backgroundColor: color.BACKGROUND,
      paddingHorizontal: SPACING.M,
      paddingTop: SPACING.SIX,
      borderTopLeftRadius: SPACING.M,
      borderTopRightRadius: SPACING.M,
    },
    title: {
      fontWeight: "300",
      fontSize: 16,
      color: color.HALF_DIMMED,
      marginBottom: SPACING.M,
    },
    inputWrapper: {
      borderBottomColor: color.BORDER_COLOR,
      borderBottomWidth: 1,
    },
    button: {
      backgroundColor: color.PRESSABLE,
      flexDirection: "row",
      alignItems: "center",
      paddingVertical: SPACING.S,
      paddingHorizontal: SPACING.M,
      borderRadius: SPACING.SIX,
      marginVertical: SPACING.XS,
    },
    buttonWrapper: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
  });

  return { styles, color };
};
