import React from "react";
import { ScrollView, StyleSheet, Text, View } from "react-native";
import getTaskExpenses from "../../queries/getTaskExpenses";

// Components
import { CustomText, Loader } from "bcomponents";

// Styles
import { SPACING } from "bstyles";
import { Theme } from "btheme/ThemeInterface";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";

type Props = {
  taskId: number;
  totalAmount: string;
};

const TaskCosts = ({ taskId, totalAmount }: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);
  const { data, isError, isLoading, error } = getTaskExpenses(taskId);

  function formatNumber(num) {
    return parseFloat(num).toLocaleString("US-EN", {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    });
  }

  const totalCost = data?.reduce((acc, item) => {
    return acc + item.cost;
  }, 0);

  const headers = [
    { key: "expenseName", value: "Cost Center" },
    { key: "budget", value: "Budget" },
    { key: "cost", value: "Cost" },
    { key: "budgetPerYearToDate", value: "Acc Budget" },
  ];

  if (isLoading) {
    return (
      <View style={styles.container}>
        <Loader />
      </View>
    );
  }

  if (isError) {
    return (
      <View style={styles.error}>
        <Text style={styles.errorText}>{error?.message}</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <CustomText
        style={{
          color: color.TEXT_DIMMED,
          marginBottom: 5,
          color: color.BRAND_DEFAULT,
        }}
      >
        Budget Analysis
      </CustomText>

      {data && data.length > 0 ? (
        <ScrollView horizontal>
          {headers?.map((header, index) => {
            return (
              <View key={header + index} style={{ marginRight: 15 }}>
                <CustomText style={{ color: color.TEXT_DIMMED }}>
                  {header.value}
                </CustomText>

                <View style={{}}>
                  {data?.map((item) => {
                    const formatValue = (value) => {
                      const numberValue = Number(value);
                      return isNaN(numberValue)
                        ? value
                        : numberValue.toFixed(2);
                    };

                    return (
                      <View key={item.id}>
                        <CustomText style={{}}>
                          {formatValue(item[header.key])}
                        </CustomText>
                      </View>
                    );
                  })}
                </View>
              </View>
            );
          })}
        </ScrollView>
      ) : (
        <CustomText></CustomText>
      )}

      <View style={styles.row}>
        <CustomText style={{ fontSize: 15, color: color.BRAND_DEFAULT }}>
          Total
        </CustomText>

        <CustomText style={{ fontSize: 15 }}>{totalAmount}</CustomText>
      </View>
    </View>
  );
};

export default TaskCosts;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      borderRadius: SPACING.SIX,
      minHeight: 100,
    },
    row: {
      flexDirection: "row",
      justifyContent: "space-between",
      paddingVertical: SPACING.XXS,
    },
    error: {
      minHeight: 100,
      backgroundColor: color.PRESSABLE,
      alignItems: "center",
      justifyContent: "center",
      borderRadius: SPACING.XS,
    },
    errorText: {
      color: color.HALF_DIMMED,
      padding: 20,
    },
  });

  return { styles, color };
};
