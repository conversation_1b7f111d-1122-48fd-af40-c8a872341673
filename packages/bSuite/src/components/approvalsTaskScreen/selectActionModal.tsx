import React, { SetStateAction, useRef, useEffect } from "react";
import { StyleSheet, KeyboardAvoidingView } from "react-native";
import { useSelector, useDispatch } from "react-redux";
import { TaskInfo, setStatusMapper } from "../../slices/approvalsSlice";

// Components
import { MyModal, CustomText, ModalCloseImage } from "bcomponents/";
import ActionItem from "./actionItem";

// Styles
import { SPACING } from "bstyles";
import { Theme } from "btheme/ThemeInterface";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";

type Props = {
  isVisible: boolean;
  setIsVisible: React.Dispatch<SetStateAction<boolean>>;
  onSelectAction: (actionId: number) => void;
  taskData: TaskInfo;
};

const actionsAvailable = {
  1: [1, 4, 6],
  2: [1, 2, 4, 6],
};

const SelectActionModal = ({
  isVisible,
  setIsVisible,
  onSelectAction,
  taskData,
}: Props) => {
  const dispatch = useDispatch();
  const { taskStatusMapper } = useSelector(
    (state) => state.root.approvalsReducer
  );

  const { styles, color } = useThemeAwareObject(createStyles);
  const actions = actionsAvailable[taskData?.canApprove];
  const taskDataRef = useRef<TaskInfo | null>(null);

  useEffect(() => {
    taskDataRef.current = taskData;

    taskData?.canApprove === 1
      ? dispatch(
          setStatusMapper({
            ...taskStatusMapper,
            1: {
              name: "Review",
              iconName: "eye-filled",
              color: color.TEXT_DEFAULT,
            },
          })
        )
      : dispatch(
          setStatusMapper({
            ...taskStatusMapper,
            1: {
              name: "Approve",
              iconName: "check-circle",
              color: color.SUCCESS,
            },
          })
        );
  }, [taskData]);

  return (
    <MyModal open={isVisible} close={() => setIsVisible(false)}>
      <KeyboardAvoidingView behavior="padding" style={styles.modalContainer}>
        <ModalCloseImage />

        <CustomText style={styles.title}>Select Action</CustomText>

        {actions?.map((id) => (
          <ActionItem
            actionName={taskStatusMapper[id]?.name}
            name={taskStatusMapper[id]?.iconName}
            iconColor={taskStatusMapper[id]?.color}
            onPress={() => onSelectAction(id)}
          />
        ))}
      </KeyboardAvoidingView>
    </MyModal>
  );
};

export default SelectActionModal;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    modalContainer: {
      backgroundColor: color.BACKGROUND,
      padding: SPACING.M,
      paddingTop: SPACING.SIX,
      borderTopLeftRadius: SPACING.M,
      borderTopRightRadius: SPACING.M,
    },
    title: {
      fontWeight: "300",
      fontSize: 16,
      color: color.HALF_DIMMED,
      marginBottom: SPACING.M,
    },
  });

  return { styles, color };
};
