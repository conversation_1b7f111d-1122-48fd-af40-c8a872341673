import React, { useState } from "react";
import { Pressable, StyleSheet, View } from "react-native";
import getTaskParticipants from "../../queries/getTaskParticipants";

// Components
import { CustomText, Loader, TitleGroupedCards } from "bcomponents";
import TaskStatusIcon from "./statusIcon";

// Styles
import { SPACING } from "bstyles";
import { Theme } from "btheme/ThemeInterface";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";
import BenefitIconSet from "engine/src/assets/fonts/icons";

type Props = {
  documentId: number | undefined | null;
  documentType: number | undefined | null;
};

const Participant = ({ id, userName, notes, result, action, responsible }) => {
  const { styles, color } = useThemeAwareObject(createStyles);
  const [expandedDescription, setExpandedDescription] = useState(false);
  const [isLongText, setIsLongText] = useState(false);
  const MAX_LINES = 1;

  const iconDict = {
    review: 8,
    reviewed: 0,
    approve: 7,
    accepted: 1,
    reject: 7,
    rejected: 2,
    "non active": 9,
    cancelled: 3,
    waiting: 4,
    clarify: 4,
    redirect: 6,
    redirected: 5,
  };

  const onTextLayout = ({ nativeEvent: { lines } }) => {
    setIsLongText(lines.length > MAX_LINES);
  };

  return (
    <View style={[styles.container, { marginBottom: SPACING.XS }]} key={id}>
      <View style={styles.row}>
        <View style={{ flex: 1, marginRight: 5 }}>
          <CustomText>{responsible}</CustomText>

          {!!userName && (
            <CustomText style={{ fontSize: 10 }}>
              Done by:{" "}
              <CustomText style={{ fontWeight: "700", fontSize: 10 }}>
                {userName}
              </CustomText>
            </CustomText>
          )}

          <CustomText style={{ fontWeight: "700", fontSize: 10 }}>
            Comments:
          </CustomText>

          <CustomText
            onTextLayout={onTextLayout}
            style={{ color: color.TEXT_DEFAULT, fontSize: 12 }}
            numberOfLines={expandedDescription ? 0 : 1}
          >
            {notes}
          </CustomText>

          <Pressable
            onPress={() => setExpandedDescription(!expandedDescription)}
          >
            {isLongText && (
              <CustomText
                style={{ color: color.TEXT_DIMMED }}
                numberOfLines={expandedDescription ? 0 : 1}
              >
                {!expandedDescription ? "...show more" : "show less"}
              </CustomText>
            )}
          </Pressable>
        </View>

        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <View
            style={{
              alignItems: "center",
              marginBottom: SPACING.XS,
            }}
          >
            <CustomText
              style={{
                fontWeight: "700",
                fontSize: 10,
                margin: SPACING.XXS,
              }}
            >
              Action Required:
            </CustomText>

            <TaskStatusIcon
              taskStatus={iconDict[action?.toLowerCase()?.trim()]}
              style={{ marginRight: 10, alignSelf: "center" }}
            />
          </View>

          {result && (
            <View
              style={{
                alignItems: "center",
                marginBottom: SPACING.XS,
              }}
            >
              <CustomText
                style={{
                  fontWeight: "700",
                  fontSize: 10,
                  margin: SPACING.XXS,
                }}
              >
                Action Performed:
              </CustomText>

              {result === "Pending" ? (
                <CustomText
                  style={{ marginRight: 27, alignSelf: "center" }}
                ></CustomText>
              ) : (
                <TaskStatusIcon
                  taskStatus={iconDict[result?.toLowerCase()?.trim()]}
                  style={{ marginRight: 10, alignSelf: "center" }}
                />
              )}
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

const Participants = ({ documentId, documentType }: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);
  const { data, isError, error, isLoading } = getTaskParticipants({
    documentId,
    documentType,
  });

  if (isLoading)
    return (
      <View style={[styles.container, { minHeight: 100 }]}>
        <Loader />
      </View>
    );

  if (isError)
    return (
      <View
        style={[
          styles.container,
          { minHeight: 100, justifyContent: "center", alignItems: "center" },
        ]}
      >
        <CustomText style={{ color: color.TEXT_DIMMED }}>
          {error?.message}
        </CustomText>
      </View>
    );

  return (
    <View>
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          marginBottom: SPACING.M,
        }}
      >
        <BenefitIconSet
          name="People"
          size={16}
          color={color.TEXT_DEFAULT}
          marginRight={SPACING.XXS}
        />
        <TitleGroupedCards title={`Participants (${data?.length})`} />
      </View>

      {data?.map((item) => {
        return (
          <Participant
            id={item.id}
            responsible={item.responsible}
            userName={item.userName}
            notes={item.notes}
            result={item.result}
            action={item.action}
          />
        );
      })}
    </View>
  );
};

export default Participants;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: color.PRESSABLE,
      padding: SPACING.S,
      borderRadius: SPACING.SIX,
      marginBottom: SPACING.M,
    },
    row: {
      // flexDirection: "row",
      // justifyContent: "space-between",
      paddingVertical: SPACING.XXS,
    },
  });

  return { styles, color };
};
