import React from "react";
import { useDispatch } from "react-redux";
import { Pressable, StyleSheet, View } from "react-native";
import { setApprovalsCategoryListFilters } from "../slices/approvalsSlice";
import { routeNames } from "../navigation/routeNames";
import { useSelector } from "react-redux";
import { useNavigation } from "@react-navigation/native";

// Components
import { CustomText } from "bcomponents";
import ListItemBadges from "./listItemBadges";
import BenefitIconSet from "engine/src/assets/fonts/icons";

// Styles
import { Theme } from "btheme/ThemeInterface";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";
import { SPACING } from "bstyles";

const ListItem = ({ title, icon, testID, onPress, disabled, isImportant }) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  return (
    <View
      style={[styles.container, { opacity: disabled ? 0.6 : 1 }]}
      testID={testID}
    >
      <Pressable
        hitSlop={20}
        onPress={() => {
          onPress ? onPress() : null;
        }}
      >
        <View style={styles.buttonContentWrapper}>
          <View style={styles.buttonContent}>
            {icon ? (
              <View style={styles.imageWrapper}>
                <BenefitIconSet
                  name={icon}
                  size={16}
                  color={color.HALF_DIMMED}
                />
              </View>
            ) : null}
            <View>
              <CustomText style={styles.buttonText}>{title}</CustomText>
            </View>
          </View>

          {isImportant === 1 && <ListItemBadges />}

          <BenefitIconSet
            name={"arrow-right"}
            size={22}
            color={color.TEXT_DIMMED}
          />
        </View>
      </Pressable>
    </View>
  );
};

const SummaryListItem = ({ item }) => {
  const dispatch = useDispatch();
  const { groupVessel, approvalsFilters } = useSelector(
    (state) => state.root.approvalsReducer
  );
  const { styles, color } = useThemeAwareObject(createStyles);
  const navigation = useNavigation();

  const onPress = () => {
    dispatch(
      setApprovalsCategoryListFilters({
        vesselId: item.vessel || approvalsFilters.vesselId,
        fleetId: item.fleet,
        categoryId: item.category,
        senderId: item.sender,
        expenseId: item.expense,
      })
    );

    navigation.navigate(
      item.number === "0" ? "" : routeNames.singleCategoryList
    );
  };

  return (
    <View style={styles.sectionItemWrapper}>
      <ListItem
        title={`${item.name} (${item.number})`}
        testID={"testID-invoice"}
        onPress={item.number === "0" ? null : onPress}
        disabled={item.number === "0"}
        isImportant={item.isImportant}
      />
    </View>
  );
};

export default SummaryListItem;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    sectionItemWrapper: {
      backgroundColor: color.BACKGROUND,
      paddingHorizontal: SPACING.M,
    },
    container: {
      backgroundColor: color.PRESSABLE,
      marginBottom: SPACING.XS,
      borderRadius: SPACING.XS,
      paddingVertical: SPACING.XL,
      paddingHorizontal: SPACING.M,
    },
    sectionTitle: {
      marginBottom: SPACING.S,
    },
    buttonContentWrapper: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    buttonContent: {
      flex: 5 / 6,
      flexDirection: "row",
      alignItems: "center",
    },
    imageWrapper: {
      paddingRight: SPACING.XS,
    },
    image: {
      width: SPACING.M,
      height: SPACING.M,
    },
    buttonText: {
      color: color.TEXT_DEFAULT,
    },
    buttonArrowContainer: {
      flex: 1 / 6,
      alignItems: "flex-end",
      paddingTop: 4,
    },
  });

  return { styles, color };
};
