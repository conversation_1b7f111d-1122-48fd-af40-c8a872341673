import React from "react";
import { Image, View } from "react-native";
import { useSelector, useDispatch } from "react-redux";
import { useNavigation } from "@react-navigation/native";
import {
  setApprovalsCategoryListFilters,
  setApprovalsListFilters,
  setSearchTerm,
} from "../slices/approvalsSlice";
import routeNames from "../navigation/routeNames";
import { useQueryClient } from "react-query";
import { queryKeys } from "../queries/queryKeys";

// Components
import AppliedFilter from "./appliedFilter";

// Styles
import { SPACING } from "bstyles";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";

const ListHeaderSearch = (props) => {
  const { isSummary } = props;
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const {
    approvalsFilters,
    approvalsCategoryFilters,
    fleets,
    vessels,
    categories,
    senders,
    expenses,
    approvals,
    approvalsListCategory,
    groupVessel,
  } = useSelector((state) => state.root.approvalsReducer);
  const { images } = useThemeAwareObject((images) => images);

  const activeFilters = isSummary ? approvalsFilters : approvalsCategoryFilters;

  const fleetName =
    fleets[approvalsFilters?.fleetId || approvalsCategoryFilters?.fleetId]
      ?.fleetName;
  const vesselName =
    vessels[approvalsFilters?.vesselId || approvalsCategoryFilters?.vesselId]
      ?.vesselName;
  const categoryName = categories[activeFilters?.categoryId]?.categoryName;
  const senderName =
    senders[approvalsFilters?.senderId || approvalsCategoryFilters?.senderId]
      ?.senderName;
  const expenseName =
    expenses[approvalsFilters?.expenseId || approvalsCategoryFilters?.expenseId]
      ?.expenseName;

  const count = isSummary
    ? approvals?.allIds?.length
    : approvalsListCategory?.allIds?.length;

  const onFilterPress = () => {
    navigation.navigate({
      name: routeNames.filterScreen,
      params: { isSummary },
    });
  };

  const onPress = async ({ name, value }) => {
    dispatch(setApprovalsListFilters({ ...approvalsFilters, [name]: value }));
    dispatch(
      setApprovalsCategoryListFilters({
        ...approvalsCategoryFilters,
        [name]: value,
      })
    );

    setTimeout(async () => {
      await queryClient.invalidateQueries(
        queryKeys.categoryApprovals({
          fleetId: approvalsCategoryFilters.fleetId,
          vesselId: approvalsCategoryFilters.vesselId,
          categoryId: approvalsCategoryFilters.categoryId,
          senderId: approvalsCategoryFilters.senderId,
          expenseId: approvalsCategoryFilters.expenseId,
        })
      );

      await queryClient.invalidateQueries(
        queryKeys.categories({
          fleetId: approvalsCategoryFilters.fleetId,
          vesselId: approvalsCategoryFilters.vesselId,
          categoryId: approvalsCategoryFilters.categoryId,
          senderId: approvalsCategoryFilters.senderId,
          expenseId: approvalsCategoryFilters.expenseId,
          groupVessel: groupVessel,
        })
      );
    }, 1000);
  };

  return (
    <View
      style={{
        marginHorizontal: SPACING.M,
        marginTop: SPACING.M,
      }}
    >
      <View
        style={{
          flexDirection: "row",
          flexWrap: "wrap",
          alignItems: "center",
          marginBottom: SPACING.M,
        }}
      >
        {fleetName ? (
          <AppliedFilter
            type={"Fleet"}
            fleetName={fleetName}
            onPress={() => onPress({ name: "fleetId", value: null })}
          />
        ) : null}

        {categoryName ? (
          <AppliedFilter
            type={"Category"}
            fleetName={categoryName}
            onPress={() => onPress({ name: "categoryId", value: null })}
            nonRemovable
          />
        ) : null}

        {vesselName ? (
          <AppliedFilter
            type={"Vessel"}
            fleetName={vesselName}
            onPress={() => onPress({ name: "vesselId", value: null })}
            // nonRemovable
          />
        ) : null}

        {senderName ? (
          <AppliedFilter
            type={"Sender"}
            fleetName={senderName}
            onPress={() => onPress({ name: "senderId", value: null })}
          />
        ) : null}

        {expenseName ? (
          <AppliedFilter
            type={"Expense"}
            fleetName={expenseName}
            onPress={() => onPress({ name: "expenseId", value: null })}
          />
        ) : null}
      </View>

      {isSummary && (
        <Image
          source={images.B_IN_CHARGE_LOGO}
          resizeMode="contain"
          style={{ width: 105, height: 16 }}
        />
      )}
    </View>
  );
};

export default ListHeaderSearch;
