import React, { useState } from "react";
import { Pressable, ScrollView, StyleSheet, View } from "react-native";
import { Picker } from "@react-native-picker/picker";
import { useQueryClient } from "react-query";
import { useDispatch, useSelector } from "react-redux";

// Components
import { CustomText } from "bcomponents";

// Styles
import { SPACING } from "bstyles";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";
import { Theme } from "btheme/ThemeInterface";
import BenefitIconSet from "engine/src/assets/fonts/icons";

import {
  setApprovalsCategoryListFilters,
  setApprovalsListFilters,
  setGroupVessel,
} from "../slices/approvalsSlice";
import { queryKeys } from "../queries/queryKeys";

const FilterScreen = ({ navigation, route }) => {
  const { isSummary } = route?.params;
  const {
    fleets,
    vessels,
    categories,
    senders,
    expenses,
    approvalsFilters,
    approvalsCategoryFilters,
    groupVessel,
  } = useSelector((state) => state.root.approvalsReducer);
  const activeFilters = isSummary ? approvalsFilters : approvalsCategoryFilters;
  const { color, styles } = useThemeAwareObject(createStyles);
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const [vesselId, setVessel] = useState(activeFilters.vesselId);
  const [categoryId, setCategory] = useState(activeFilters.categoryId);
  const [fleetId, setFleet] = useState(activeFilters.fleetId);
  const [senderId, setSender] = useState(activeFilters.senderId);
  const [expenseId, setExpense] = useState(activeFilters.expenseId);
  const [pickerFocused, setPickerFocused] = useState(false);

  const onPress = async () => {
    dispatch(
      setApprovalsListFilters({
        vesselId,
        categoryId,
        fleetId,
        senderId,
        expenseId,
      })
    );
    dispatch(
      setApprovalsCategoryListFilters({
        vesselId,
        categoryId,
        senderId,
        expenseId,
      })
    );

    navigation.goBack();
  };

  const onResetPress = () => {
    dispatch(
      setApprovalsListFilters({
        vesselId: "",
        categoryId: "",
        fleetId: "",
        senderId: "",
        expenseId: "",
      })
    );
    dispatch(
      setApprovalsCategoryListFilters({
        vesselId: "",
        categoryId: categoryId,
        senderId: "",
        expenseId: "",
      })
    );

    setVessel("");
    setCategory("");
    setFleet("");
    setSender("");
    setExpense("");

    navigation.goBack();
  };

  const onFleetChange = async (itemValue) => {
    dispatch(
      setApprovalsListFilters({
        vesselId: "",
        categoryId: approvalsFilters.categoryId,
        fleetId: itemValue,
        senderId: approvalsFilters.senderId,
        expenseId: approvalsFilters.expenseId,
      })
    );

    await queryClient.invalidateQueries([
      queryKeys.categories({
        vesselId: "",
        categoryId: "",
        fleetId: itemValue,
        senderId: "",
        expenseId: "",
        groupVessel,
      }),
    ]);

    await queryClient.invalidateQueries(
      queryKeys.categoryApprovals({
        fleetId: approvalsCategoryFilters.fleetId,
        vesselId: approvalsCategoryFilters.vesselId,
        categoryId: approvalsCategoryFilters.categoryId,
        senderId: approvalsCategoryFilters.senderId,
        expenseId: approvalsCategoryFilters.expenseId,
      })
    );

    await queryClient.invalidateQueries(queryKeys.vessels({ fleetId }));
    setFleet(itemValue);
    setVessel("");
  };

  const handleGroupVessel = async () => {
    dispatch(setGroupVessel(!groupVessel));
    await queryClient.invalidateQueries(
      queryKeys.categories({
        vesselId: "",
        categoryId: "",
        fleetId: "",
        senderId: "",
        expenseId: "",
        groupVessel,
      })
    );
  };

  return (
    <View style={{ flex: 1, backgroundColor: color.BACKGROUND }}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={{
          backgroundColor: color.BACKGROUND,
          padding: 10,
        }}
        contentContainerStyle={{ paddingBottom: 50 }}
      >
        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            backgroundColor: color.PRESSABLE,
            paddingTop: SPACING.XS,
            paddingHorizontal: SPACING.S,
            borderRadius: SPACING.S,
            marginBottom: SPACING.S,
          }}
        >
          <View style={{ flex: 1 }}>
            <CustomText style={{ fontWeight: "700", fontSize: 12 }}>
              Fleet
            </CustomText>

            <Picker
              mode="dropdown"
              onFocus={() => setPickerFocused(true)}
              onBlur={() => setPickerFocused(false)}
              selectedValue={fleetId}
              onValueChange={onFleetChange}
              dropdownIconColor={color.HALF_DIMMED}
            >
              <Picker.Item
                label={"Select Fleet"}
                value={""}
                color={color.TEXT_DIMMED}
              />

              {fleets.allIds.map((id) => {
                return (
                  <Picker.Item
                    key={id}
                    value={id}
                    label={fleets[id].fleetName}
                    color={color.TEXT_DEFAULT}
                  />
                );
              })}
            </Picker>
          </View>
        </View>

        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            backgroundColor: color.PRESSABLE,
            paddingTop: SPACING.XS,
            paddingHorizontal: SPACING.S,
            borderRadius: SPACING.S,
            marginBottom: SPACING.S,
          }}
        >
          <View style={{ flex: 4 }}>
            <CustomText style={{ fontWeight: "700", fontSize: 12 }}>
              Vessel
            </CustomText>

            <Picker
              mode="dropdown"
              onFocus={() => setPickerFocused(true)}
              onBlur={() => setPickerFocused(false)}
              selectedValue={vesselId}
              onValueChange={(itemValue) => {
                setVessel(itemValue);
              }}
              dropdownIconColor={color.HALF_DIMMED}
            >
              <Picker.Item
                label={"Select Vessel"}
                value={""}
                color={color.TEXT_DIMMED}
              />

              {vessels.allIds.map((id) => {
                return (
                  <Picker.Item
                    key={id}
                    value={id}
                    label={vessels[id].vesselName}
                    color={color.TEXT_DEFAULT}
                  />
                );
              })}
            </Picker>
          </View>
        </View>

        {isSummary ? (
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              backgroundColor: color.PRESSABLE,
              paddingTop: SPACING.XS,
              paddingHorizontal: SPACING.S,
              borderRadius: SPACING.S,
              marginBottom: SPACING.S,
            }}
          >
            <View style={{ flex: 4 }}>
              <CustomText style={{ fontWeight: "700", fontSize: 12 }}>
                Category
              </CustomText>

              <Picker
                mode="dropdown"
                onFocus={() => setPickerFocused(true)}
                onBlur={() => setPickerFocused(false)}
                selectedValue={categoryId}
                onValueChange={(itemValue) => {
                  setCategory(itemValue);
                }}
                dropdownIconColor={color.HALF_DIMMED}
              >
                <Picker.Item
                  label={"Select Category"}
                  value={""}
                  color={color.TEXT_DIMMED}
                />

                {categories.allIds.map((id) => {
                  return (
                    <Picker.Item
                      key={id}
                      value={id}
                      label={categories[id].categoryName}
                      color={color.TEXT_DEFAULT}
                    />
                  );
                })}
              </Picker>
            </View>
          </View>
        ) : null}

        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            backgroundColor: color.PRESSABLE,
            paddingTop: SPACING.XS,
            paddingHorizontal: SPACING.S,
            borderRadius: SPACING.S,
            marginBottom: SPACING.S,
          }}
        >
          <View style={{ flex: 4 }}>
            <CustomText style={{ fontWeight: "700", fontSize: 12 }}>
              Sender
            </CustomText>

            <Picker
              mode="dropdown"
              onFocus={() => setPickerFocused(true)}
              onBlur={() => setPickerFocused(false)}
              selectedValue={senderId}
              onValueChange={(itemValue) => {
                setSender(itemValue);
              }}
              dropdownIconColor={color.HALF_DIMMED}
            >
              <Picker.Item
                label={"Select Sender"}
                value={""}
                color={color.TEXT_DIMMED}
              />

              {senders.allIds.map((id) => {
                return (
                  <Picker.Item
                    key={id}
                    value={id}
                    label={senders[id].senderName}
                    color={color.TEXT_DEFAULT}
                  />
                );
              })}
            </Picker>
          </View>
        </View>

        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            backgroundColor: color.PRESSABLE,
            paddingTop: SPACING.XS,
            paddingHorizontal: SPACING.S,
            borderRadius: SPACING.S,
            marginBottom: SPACING.S,
          }}
        >
          <View style={{ flex: 4 }}>
            <CustomText style={{ fontWeight: "700", fontSize: 12 }}>
              Expense
            </CustomText>

            <Picker
              mode="dropdown"
              onFocus={() => setPickerFocused(true)}
              onBlur={() => setPickerFocused(false)}
              selectedValue={expenseId}
              onValueChange={(itemValue) => {
                setExpense(itemValue);
              }}
              dropdownIconColor={color.HALF_DIMMED}
            >
              <Picker.Item
                label={"Select Expense"}
                value={""}
                color={color.TEXT_DIMMED}
              />

              {expenses.allIds.map((id) => {
                return (
                  <Picker.Item
                    key={id}
                    value={id}
                    label={expenses[id].expenseName}
                    color={color.TEXT_DEFAULT}
                  />
                );
              })}
            </Picker>
          </View>
        </View>

        {isSummary ? (
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              marginTop: SPACING.M,
            }}
          >
            <Pressable
              style={styles.checkbox}
              onPress={() => handleGroupVessel()}
            >
              {groupVessel && (
                <BenefitIconSet
                  name="check"
                  size={30}
                  color={color.PIN_ACTIVE}
                />
              )}
            </Pressable>

            <CustomText>Group by Vessel</CustomText>
          </View>
        ) : null}
      </ScrollView>

      <Pressable style={styles.button} onPress={onResetPress}>
        <CustomText style={styles.buttonText}>Reset all filters</CustomText>
      </Pressable>

      <Pressable
        style={[styles.button, { backgroundColor: color.BRAND_DEFAULT }]}
        onPress={onPress}
      >
        <CustomText style={styles.buttonText}>Apply filters</CustomText>
      </Pressable>
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    inputWrapper: {
      alignItems: "center",
      flexDirection: "row",
      borderBottomColor: color.BORDER_COLOR_FORM,
      borderBottomWidth: 1,
    },
    divider: {
      borderBottomWidth: 1,
      marginVertical: 10,
      borderColor: "grey",
    },
    button: {
      backgroundColor: color.PRESSABLE,
      alignItems: "center",
      borderRadius: SPACING.SIX,
      marginBottom: SPACING.S,
      marginHorizontal: SPACING.M,
      padding: 10,
    },
    buttonText: {
      color: color.TEXT_DEFAULT,
      fontSize: 15,
      fontWeight: "600",
    },
    checkbox: {
      width: 30,
      height: 30,
      backgroundColor: color.PRESSABLE_HOVER,
      borderColor: color.BORDER_COLOR_FORM,
      borderWidth: 1,
      borderRadius: 5,
      marginRight: 10,
    },
  });
  return { color, styles };
};

export default FilterScreen;
