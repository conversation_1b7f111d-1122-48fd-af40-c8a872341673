import React from "react";
import { Pressable, StyleSheet, View } from "react-native";
import { CustomText, Tag } from "bcomponents";

// Styles
import { SPACING } from "bstyles";
import { Theme } from "btheme/ThemeInterface";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";
import BenefitIconSet from "engine/src/assets/fonts/icons";

const AppliedFilter = ({ type, fleetName, onPress, nonRemovable }) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  return (
    <View style={styles.container}>
      <View style={styles.titleWrapper}>
        <CustomText style={{ fontSize: 10 }}>{type}: </CustomText>

        <CustomText style={{ fontWeight: "700", fontSize: 10 }}>
          {fleetName}
        </CustomText>
      </View>

      {!nonRemovable && (
        <Pressable
          onPress={onPress}
          style={{
            padding: 4,
            marginLeft: SPACING.S,
          }}
        >
          <BenefitIconSet
            name="x"
            size={11}
            color={color.DESTRUCTIVE_DEFAULT}
          />
        </Pressable>
      )}
    </View>
  );
};

export default AppliedFilter;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginRight: SPACING.XS,
      marginBottom: SPACING.XS,
      paddingHorizontal: SPACING.XXS,
      paddingVertical: SPACING.XXS,
      borderRadius: SPACING.SIX,
      backgroundColor: color.PRESSABLE,
    },
    titleWrapper: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    textStyle: {
      color: color.TEXT_DEFAULT,
      fontSize: 12,
    },
  });

  return { styles, color };
};
