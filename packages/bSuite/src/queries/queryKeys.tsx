const validateQuery = (query, arg) => {
  return arg ? [query, arg] : [query];
};

export const queryKeys = {
  baseUrl: ["baseUrl"],
  userFlow: ["userFlow"],
  fleetList: ["fleetList"],
  seamanAttachments: (seamanId?) =>
    validateQuery("seamanAttachments", seamanId),
  vessel: ["vessel"],
  vesselDetails: ["vessel-details"],
  vesselDetailsDocuments: ["vesselDetailsDocuments"],
  vesselPortCardAgents: ["vesselPortCardAgents"],
  crewListAll: (vessel_ID?) => validateQuery("crewListAll", vessel_ID),
  crewListOfficers: (vessel_ID?) =>
    validateQuery("crewListOfficers", vessel_ID),
  lastNPR: (vessel_ID?) => validateQuery("lastNPR", vessel_ID),
  nextPortCalls: (vessel_ID?) => validateQuery("nextPortCalls", vessel_ID),
  vesselDetailsGeneral: (vessel_ID?) =>
    validateQuery("vesselDetailsGeneral", vessel_ID),
  seamanPhoto: (seamanId?) => validateQuery("seamanPhoto", seamanId),
  vesselSeamanSeaservices: (seamanId?) =>
    validateQuery("vesselSeamanSeaservices", seamanId),
  vesselSeamanInfo: (seamanId?) => validateQuery("vesselSeamanInfo", seamanId),
  vesselSeamanCertificates: (seamanId?) =>
    validateQuery("vesselSeamanCertificates", seamanId),
  vesselPortAgents: (oproute_id?) =>
    validateQuery("vesselPortAgents", oproute_id),
  vesselPortAttachments: (portId?) =>
    validateQuery("vesselPortAttachments", portId),
  vesselPortInfo: (portId?) => validateQuery("vesselPortInfo", portId),
  vesselPortWorks: (oproute_id?) => ["vesselPortWorks", oproute_id],
  vesselRoute: (vessel_ID, vessel_LEG_ID) => [
    "vesselRoute",
    vessel_ID,
    vessel_LEG_ID,
  ],
  comments: ["comments"],
  categories: ({ fleetId, vesselId, categoryId, senderId, expenseId, groupVessel }) => validateQuery("categories", { fleetId, vesselId, categoryId, senderId, expenseId, groupVessel }),
  fleets: ["fleets"],
  vessels: ({ fleetId }) => validateQuery("vessels", { fleetId }),
  senders: ["senders"],
  expenses: ["expenses"],
  categoryApprovals: ({ fleetId, vesselId, categoryId, senderId, expenseId }) => validateQuery("categoryApprovals", { fleetId, vesselId, categoryId, senderId, expenseId }),
  taskInfo: ({ usractPndId, documentType }) => validateQuery("taskInfo", { usractPndId, documentType }),
  taskParticipants: ({ documentId, documentType }) => validateQuery("taskParticipants", { documentId, documentType }),
  taskExpenses: ({ usractPndId }) => validateQuery("taskExpenses", { usractPndId }),
  responsibles: ["responsibles"],
};
