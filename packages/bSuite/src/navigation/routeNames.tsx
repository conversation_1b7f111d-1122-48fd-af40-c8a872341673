const routeNames = {
  LOGIN: "LogIn",
  HOME: "Home",
  FLEET_STACK: "FleetStack",
  FLEET_VESSEL_STACK: "FleetVesselStack",
  FLEET_LIST: "Fleetlist",
  FLEET_SEARCH_LIST: "SearchScreen",
  FLEET_MAP_HOC: "MapHOC",
  FLEET_MAP_ROUTES_HOC: "MapRoutesHOC",
  VESSEL_STACK: "VesselStack",
  VESSEL_HOC: "Vessel",
  VESSEL_DETAILS_NAV: "VesselDetailsCard",
  VESSEL_DETAILS_GENERAL: "General",
  VESSEL_DETAILS_DIMENSIONS: "Dimesions",
  VESSEL_DETAILS_ENGINE: "EngineSpeed",
  VESSEL_DETAILS_DOCUMENTS: "Documents",
  VESSEL_PORT_CALLS_NAV: "VesselPortCallsCard",
  VESSEL_PORT_CALLS_WORKS: "Works",
  VESSEL_PORT_CALLS_AGENTS: "Agents",
  VESSEL_PORT_CARD_NAV: "VesselPortCard",
  VESSEL_PORT_CARD_INFO: "Info",
  VESSEL_PORT_CARD_ATTACHMENTS: "Attachments",
  VESSEL_PORT_CARD_PORTAGENTS: "Agents",
  VESSEL_CREW_NAV: "VesselSeamanCard",
  VESSEL_CREW_INFO: "Info",
  VESSEL_CREW_SEASERVICES: "Sea Services",
  VESSEL_CREW_CERTIFICATES: "Certificates",
  VESSEL_CREW_DOCUMENTS: "Documents",
  MENU_STACK: "Menu",
  MENU_HOC: "MenuHOC",
  THEME: "Theme",
  COMMENTS_STACK: "Comments",
  COMMENT_HOC: "Comment",
  NEW_MEMO: "New Memo",
  USER_FLOW_SCREEN_HOC: "UserFlowScreenHOC",
  TABS_NAVIGATION: "TabsNavigation",
  DELETE_ACCOUNT: "DeleteAccountHOC",
  singleCategoryList: "SingleCategoryList",
  ApprovalTaskHOC: "ApprovalTaskHOC",
  EvaluationTaskHOC: "EvaluationTaskHOC",
  myTasks: "My Approval Tasks",
  filterScreen: "Filter Parameters",
};

export { routeNames };
