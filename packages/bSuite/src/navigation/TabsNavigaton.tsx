import React from "react";
import { Platform, StyleSheet, View } from "react-native";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { useAuthContext } from "../contexts/AuthContext";
import env from "react-native-config";

// Components
import FleetVesselStack from "./fleetVesselStack";
import MenuStack from "./menu/menuStack";
import { routeNames } from "./routeNames";
import CommentsStack from "./comments/commentsStack";

//Styling
import { Theme } from "btheme/ThemeInterface";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";
import BenefitIconSet from "engine/src/assets/fonts/icons";
import ApprovalsStack from "./ApprovalsStack";

const Tab = createBottomTabNavigator();

const TabsNavigation = () => {
  const { styles, color } = useThemeAwareObject(createStyles);
  const { token, companyInfo, getToken } = useAuthContext();

  return (
    <View style={styles.container}>
      <Tab.Navigator
        initialRouteName={routeNames.FLEET_VESSEL_STACK}
        screenOptions={{
          headerShown: false,
          tabBarStyle: {
            backgroundColor: color.TAB_BACKGROUND,
            height: 80,
            elevation: 0,
            paddingHorizontal: 8,
          },
          tabBarLabelStyle: { fontSize: 12 },
          tabBarActiveTintColor: color.BOTTOM_TAB_ACTIVE_COLOR_BSUITE,
          tabBarInactiveTintColor: color.TEXT_DIMMED,
          tabBarActiveBackgroundColor: color.TAB_BACKGROUND_ACTIVE,
        }}
      >
        <Tab.Screen
          name={routeNames.FLEET_VESSEL_STACK}
          component={FleetVesselStack}
          options={({ navigation }) => {
            return {
              tabBarLabel: "InTouch",
              tabBarItemStyle: {
                padding: 10,
                marginVertical: 5,
                borderRadius: 6,
                borderBottomColor: navigation.isFocused()
                  ? "#88cc29"
                  : "transparent",
                borderBottomWidth: 5,
              },
              tabBarIcon: ({ focused }) => (
                <BenefitIconSet
                  name="cargo-ship"
                  size={25}
                  color={
                    focused
                      ? color.BOTTOM_TAB_ACTIVE_COLOR_BSUITE
                      : color.TEXT_DIMMED
                  }
                />
              ),
            };
          }}
        />

        <Tab.Screen
          name={"Approvals Tab"}
          listeners={{
            tabPress: (e) => {
              env.APPROVALS === "false" && e.preventDefault();
            },
          }}
          component={ApprovalsStack}
          options={({ navigation }) => {
            return {
              tabBarLabel: "InCharge",
              tabBarLabelStyle: {
                fontSize: 12,
                color:
                  env.APPROVALS === "false"
                    ? color.TEXT_DISABLED
                    : color.TEXT_DEFAULT,
              },
              tabBarBadge: env.APPROVALS === "false" ? "Coming Soon" : null,
              tabBarBadgeStyle: {
                backgroundColor: color.TAB_BACKGROUND_ACTIVE,
                fontSize: 6,
              },
              tabBarItemStyle: {
                padding: 10,
                marginVertical: 5,
                borderRadius: 6,
                borderBottomColor: navigation.isFocused()
                  ? "#88cc29"
                  : "transparent",
                borderBottomWidth: 5,
              },
              tabBarIcon: ({ focused }) => (
                <BenefitIconSet
                  name={"Quick-View"}
                  size={25}
                  color={
                    env.APPROVALS === "false"
                      ? color.TEXT_DISABLED
                      : focused
                      ? color.BOTTOM_TAB_ACTIVE_COLOR_BSUITE
                      : color.TEXT_DIMMED
                  }
                />
              ),
            };
          }}
        />

        {env.COMMENTS === "true" && (
          <Tab.Screen
            name={"Comments Tab"}
            listeners={{
              tabPress: (e) => {
                env.COMMENTS === "true" && e.preventDefault();
              },
            }}
            component={CommentsStack}
            options={({ navigation }) => {
              return {
                tabBarLabel: "Comments",
                tabBarLabelStyle: {
                  fontSize: 12,
                  color:
                    env.COMMENTS === "true"
                      ? color.TEXT_DISABLED
                      : color.TEXT_DIMMED,
                },
                tabBarBadge: env.COMMENTS === "true" ? "Coming Soon" : null,
                tabBarBadgeStyle: {
                  backgroundColor: color.PRESSABLE_HOVER,
                  fontSize: 6,
                },
                tabBarItemStyle: {
                  padding: 10,
                  marginVertical: 5,
                  borderRadius: 6,
                  borderBottomColor: navigation.isFocused()
                    ? "#88cc29"
                    : "transparent",
                  borderBottomWidth: 5,
                },
                tabBarIcon: ({ focused }) => (
                  <BenefitIconSet
                    name={"message"}
                    size={25}
                    color={
                      env.COMMENTS === "true"
                        ? color.TEXT_DISABLED
                        : focused
                        ? color.BOTTOM_TAB_ACTIVE_COLOR_BSUITE
                        : color.TEXT_DIMMED
                    }
                  />
                ),
              };
            }}
          />
        )}

        <Tab.Screen
          name={routeNames.MENU_STACK}
          component={MenuStack}
          options={({ navigation }) => {
            return {
              tabBarItemStyle: {
                padding: 10,
                marginVertical: 5,
                borderRadius: 6,
                borderBottomColor: navigation.isFocused()
                  ? "#88cc29"
                  : "transparent",
                borderBottomWidth: 5,
              },
              tabBarIcon: ({ focused }) => (
                <BenefitIconSet
                  name="more-horizontal"
                  size={25}
                  color={
                    focused
                      ? color.BOTTOM_TAB_ACTIVE_COLOR_BSUITE
                      : color.TEXT_DIMMED
                  }
                />
              ),
            };
          }}
        />
      </Tab.Navigator>
    </View>
  );
};

export default TabsNavigation;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.HEADER_COLOR,
    },
  });

  return { styles, color };
};
