import React, { useEffect, useState } from "react";
import { RefreshControl, ScrollView, StyleSheet, View } from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { useNavigation, useIsFocused } from "@react-navigation/native";
import { useIsForeground } from "../../../bAudit/src/helpers/useIsForeground";
import { useQueryClient } from "react-query";
import {
  TaskInfo,
  setLastPerformedActionItem,
  setTaskData,
} from "../slices/approvalsSlice";
import { queryKeys } from "../queries/queryKeys";
import getTaskInfo from "../queries/getTaskInfo";

// Components
import { CustomText, Loader } from "bcomponents";
import DateCard from "../components/approvalsTaskScreen/dateCard";
import Participants from "../components/approvalsTaskScreen/participants";
import TaskComments from "../components/approvalsTaskScreen/taskComments";
import StatusCard from "../components/approvalsTaskScreen/statusCard";
import TaskCosts from "../components/approvalsTaskScreen/taskCosts";
import ApprovalTaskHeader from "../components/approvalsTaskScreen/approvalTaskHeader";

// Styles
import { SPACING } from "bstyles";
import { Theme } from "btheme/ThemeInterface";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";
import BenefitIconSet from "engine/src/assets/fonts/icons";

type Props = {
  taskId: number | null;
};

const ApprovalTaskHOC = ({ route }) => {
  const navigation = useNavigation();
  const taskNumber = route?.params?.taskNumber;
  console.log("🚀 ~ ApprovalTaskHOC ~ taskNumber:", taskNumber)
  const documentType = route?.params?.documentType;
  console.log("🚀 ~ ApprovalTaskHOC ~ documentType:", documentType)
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const isFocused = useIsFocused();
  const isForeground = useIsForeground();
  const isActive = isFocused && isForeground;
  const { styles, color } = useThemeAwareObject(createStyles);
  const { data, isLoading, error, isError, isFetching } = getTaskInfo({
    usractPndId: taskNumber,
    documentType,
  });
  const taskData: TaskInfo = useSelector(
    (state) => state.root.approvalsReducer.taskData
  );
  const isConnectedToNetwork = useSelector(
    (state) => state.root.approvalsReducer.isConnectedToNetwork
  );

  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = async () => {
    setRefreshing(true);

    await queryClient.invalidateQueries(queryKeys.taskInfo);

    setRefreshing(false);
  };

  useEffect(() => {
    navigation.setOptions({
      title: taskData.vesselName,
    });
  }, [navigation, taskData]);

  useEffect(() => {
    if (isConnectedToNetwork && isActive) {
      queryClient.invalidateQueries(queryKeys.taskInfo);
    }
  }, [isConnectedToNetwork, isActive]);

  useEffect(() => {
    if (!isLoading && data) {
      if (Array.isArray(data) && data?.length === 0) {
        navigation.goBack();

        return;
      } else {
        dispatch(setTaskData(data));
      }
    }
  }, [isLoading, data]);

  const setLastActionPerformed = ({ id, status }) => {
    dispatch(setLastPerformedActionItem({ id, status }));
  };

  if (isLoading || isFetching) {
    return (
      <View style={styles.container}>
        <Loader />
      </View>
    );
  }

  if (!isConnectedToNetwork) {
    return (
      <View
        style={[
          styles.container,
          {
            flexDirection: "row",
            justifyContent: "center",
            alignItems: "center",
            padding: 50,
          },
        ]}
      >
        <BenefitIconSet
          name="x-circle"
          marginRight={10}
          color={color.DESTRUCTIVE_DEFAULT}
          size={24}
        />
        <CustomText>
          Please check your internet connection and try again
        </CustomText>
      </View>
    );
  }

  if (isError) {
    return (
      <View
        style={[
          styles.container,
          {
            flexDirection: "row",
            justifyContent: "center",
            alignItems: "center",
          },
        ]}
      >
        <BenefitIconSet
          name="x-circle"
          marginRight={10}
          color={color.DESTRUCTIVE_DEFAULT}
          size={24}
        />
        <CustomText>{error?.message}</CustomText>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        refreshControl={
          <RefreshControl
            tintColor={color.TEXT_DIMMED}
            colors={[color.TEXT_DIMMED]}
            refreshing={refreshing}
            onRefresh={onRefresh}
          />
        }
        style={{ padding: SPACING.M, marginBottom: SPACING.M }}
      >
        <ApprovalTaskHeader
          type={documentType === 70 ? "Invoice" : "Order"}
          vesselName={taskData.vesselName}
          documentNumber={taskData.taskId}
          taskRef={taskData.taskRef}
          taskId={taskNumber}
          setLastActionPerformedItem={setLastActionPerformed}
        />

        <View style={styles.taskDetailsWrapper}>
          <StatusCard
            supplier={taskData.supplier}
            date={taskData.taskDate}
            taskStatus={taskData.taskStatus}
          />

          <TaskCosts
            taskId={taskNumber}
            totalAmount={taskData.orderAmount || taskData.amount}
          />

          <DateCard title="Issued Date" date={taskData.taskDate} />

          <DateCard title="Order Date" date={taskData.orderDate} />
        </View>

        <TaskComments taskComments={taskData.taskComments} />

        <Participants
          documentId={taskData.taskId}
          documentType={documentType}
        />
      </ScrollView>
    </View>
  );
};

export default ApprovalTaskHOC;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
    },
    taskDetailsWrapper: {
      backgroundColor: color.PRESSABLE,
      padding: SPACING.M,
      borderRadius: SPACING.SIX,
      marginBottom: SPACING.M,
    },
  });

  return { styles, color };
};
