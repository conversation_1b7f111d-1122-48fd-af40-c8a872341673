import React, { useEffect, useState } from "react";
import { View, Image } from "react-native";
import { useSelector, useDispatch } from "react-redux";
import { useQueryClient } from "react-query";
import NetInfo from "@react-native-community/netinfo";
import { useIsForeground } from "../../../bAudit/src/helpers/useIsForeground";
import { useIsFocused } from "@react-navigation/native";

//Components
import { CustomText, Loader } from "bcomponents";
import ApprovalsList from "../components/approvalsList";

// Redux & queries
import { queryKeys } from "../queries/queryKeys";
import {
  setApprovals,
  setExpenses,
  setFleets,
  setIsConnectedToNetwork,
  setResponsibles,
  setSenders,
  setStatusMapper,
  setVessels,
} from "../slices/approvalsSlice";
import getCategories from "../queries/getCategories";
import getFleets from "../queries/getFleets";
import getSenders from "../queries/getSenders";
import getExpenses from "../queries/getExpenses";
import getVessels from "../queries/getVessels";
import getTaskResponsibles from "../queries/getTaskResponsibles";

// Styles
import { FONTSIZES } from "bstyles";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";

type Props = {};

const ApprovalsCategoriesHOC = ({}: Props) => {
  const dispatch = useDispatch();
  const { color } = useThemeAwareObject((color) => color);
  const { images } = useThemeAwareObject((images) => images);

  const queryClient = useQueryClient();
  const isFocused = useIsFocused();
  const isForeground = useIsForeground();
  const isActive = isFocused && isForeground;
  const {
    approvals,
    vessels,
    approvalsFilters,
    categories,
    groupVessel,
    isConnectedToNetwork,
  } = useSelector((state) => state.root.approvalsReducer);

  const { data, error, isError, isLoading, isFetching } = getCategories(
    approvalsFilters.fleetId,
    approvalsFilters.vesselId,
    approvalsFilters.categoryId,
    approvalsFilters.senderId,
    approvalsFilters.expenseId,
    groupVessel
  );
  const { data: fleetsData, isLoading: getFleetsIsLoading } = getFleets();
  const { data: vesselsData, isLoading: getVesseIsLoading } = getVessels(
    approvalsFilters.fleetId
  );

  const { data: sendersData, isLoading: getSendersIsLoading } = getSenders();
  const { data: expensesData, isLoading: getExpensesIsLoading } = getExpenses();
  const { data: responsiblesData, isLoading: getResponsiblesIsLoading } =
    getTaskResponsibles();

  useEffect(() => {
    if (isConnectedToNetwork && isActive) {
      queryClient.invalidateQueries(queryKeys.categories);
    }
  }, [isConnectedToNetwork, isActive]);

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener((state) => {
      dispatch(setIsConnectedToNetwork(state.isConnected));
    });

    return () => unsubscribe();
  }, []);

  useEffect(() => {
    if (!isLoading && data) {
      dispatch(setApprovals(data));
    }
  }, [isLoading, data, queryClient]);

  useEffect(() => {
    if (!getFleetsIsLoading) {
      dispatch(setFleets(fleetsData));
    }
  }, [getFleetsIsLoading, fleetsData]);

  useEffect(() => {
    if (!getVesseIsLoading) {
      dispatch(setVessels(vesselsData));
    }
  }, [getVesseIsLoading, vesselsData]);

  useEffect(() => {
    if (!getSendersIsLoading) {
      dispatch(setSenders(sendersData));
    }
  }, [getSendersIsLoading, sendersData]);

  useEffect(() => {
    if (!getResponsiblesIsLoading) {
      dispatch(setResponsibles(responsiblesData));
    }
  }, [getResponsiblesIsLoading, responsiblesData]);

  useEffect(() => {
    if (!getExpensesIsLoading) {
      dispatch(setExpenses(expensesData));
    }
  }, [getExpensesIsLoading, expensesData]);

  useEffect(() => {
    const statusMapper = {
      0: {
        name: "Pending",
        iconName: "check-circle",
        color: color.TEXT_DEFAULT,
      },
      1: { name: "Approve", iconName: "check-circle", color: color.SUCCESS },
      2: {
        name: "Reject",
        iconName: "x-circle",
        color: color.DESTRUCTIVE_DEFAULT,
      },
      3: {
        name: "Disabled",
        iconName: "disabled",
        color: color.DESTRUCTIVE_DEFAULT,
      },
      4: {
        name: "Set as Waiting",
        iconName: "clock",
        color: color.TEXT_DEFAULT,
      },
      5: {
        name: "Returned",
        iconName: "corner-down-left",
        color: color.TEXT_DEFAULT,
      },
      6: {
        name: "Redirect",
        iconName: "arrow-up-right",
        color: color.TEXT_DEFAULT,
      },
      7: {
        name: "Review",
        iconName: "eye-filled",
        color: color.TEXT_DEFAULT,
      },
      allIds: [0, 1, 2, 3, 4, 5, 6],
    };

    dispatch(setStatusMapper(statusMapper));
  }, []);

  const [refreshing, setRefreshing] = useState(false);
  const onRefresh = async () => {
    setRefreshing(true);

    await queryClient.invalidateQueries(queryKeys.categories);

    setRefreshing(false);
  };

  const sortedData = groupVessel
    ? approvals.allIds
        ?.map((id) => approvals[id].vesselId)
        .filter((value, index, array) => array.indexOf(value) === index)
        .map((vesselId) => {
          return {
            title: {
              name: vessels[vesselId]?.vesselName,
              sumOfApprovals: approvals.allIds.filter(
                (id) => approvals[id]?.vesselId === vesselId
              ).length,
            },
            data: approvals.allIds
              .filter((id) => approvals[id].vesselId === vesselId)
              .map((id) => approvals[id].category)
              .filter((value, index, array) => array.indexOf(value) === index)
              .map((categoryName) => {
                return {
                  name: categoryName,
                  number:
                    approvals[
                      approvals.allIds
                        .filter((id) => approvals[id].vesselId === vesselId)
                        .filter(
                          (approvalId) =>
                            approvals[approvalId].category === categoryName
                        )
                    ]?.number || "0",
                  isImportant: approvals.allIds
                    .filter((id) => approvals[id]?.vesselId === vesselId)
                    .some(
                      (approvalId) => approvals[approvalId]?.importance === 1
                    )
                    ? 1
                    : 0,
                  vessel: vesselId,
                  sender: approvalsFilters?.senderId,
                  expense: approvalsFilters?.expenseId,
                  fleet: approvalsFilters?.fleetId,
                  category: categories.allIds.filter(
                    (categoryId) =>
                      categories[categoryId].categoryName === categoryName
                  )[0],
                };
              }),
          };
        })
        .sort((a, b) => {
          return (
            b.data.filter((dato) => dato.isImportant === 1).length -
            a.data.filter((dato) => dato.isImportant === 1).length
          );
        })
    : [
        {
          title: [
            {
              name: "All vessels",
              sumOfApprovals: 1,
            },
          ],
          data: categories.allIds.map((id) => {
            const categoryName = categories[id]?.categoryName;

            return {
              name: categoryName,
              number:
                approvals[
                  approvals.allIds.filter(
                    (approvalId) =>
                      approvals[approvalId]?.category === categoryName
                  )
                ]?.number || "0",
              isImportant: approvals.allIds.some(
                (approvalId) =>
                  approvals[approvalId]?.category === categoryName &&
                  approvals[approvalId]?.importance === 1
              )
                ? 1
                : 0,
              sender: approvalsFilters?.senderId,
              expense: approvalsFilters?.expenseId,
              fleet: approvalsFilters?.fleetId,
              category: categories.allIds.filter(
                (categoryId) =>
                  categories[categoryId].categoryName === categoryName
              )[0],
            };
          }),
        },
      ];

  if (isLoading || isFetching) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: color.BACKGROUND,
        }}
      >
        <Loader />
      </View>
    );
  }

  if (isError) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: color.BACKGROUND,
        }}
      >
        <CustomText style={{ fontSize: FONTSIZES.TWENTY_FOUR }}>
          {error != null ? error?.message : <></>}
        </CustomText>
      </View>
    );
  }

  return (
    <View style={{ flex: 1 }}>
      <ApprovalsList
        data={sortedData}
        isSummary={true}
        refresh={refreshing}
        onRefresh={onRefresh}
      />
    </View>
  );
};

export default ApprovalsCategoriesHOC;
